<?php
require_once '../config/config.php';
requireAdmin(); // Or requireLogin() if users configure their own 2FA

// Include Google Authenticator library (ensure it's installed via Composer or manually)
require_once '../vendor/autoload.php'; // Assuming SonoriticsGoogleAuthenticator is in vendor
use Sonata\GoogleAuthenticator\GoogleAuthenticator;
use Sonata\GoogleAuthenticator\GoogleQrUrl;

$page_title = 'Configure 2FA (Google Authenticator)';
$user_id = $_SESSION['user_id']; // Or the ID of the user being configured if admin is setting it up

$db = getDB();
$g = new GoogleAuthenticator();
$secret = null;
$qrCodeUrl = null;
$user_2fa_enabled = false;
$feedback_message = '';
$feedback_type = '';

// Check if user already has 2FA configured
try {
    $stmt = $db->prepare("SELECT google_authenticator_secret FROM user_security_settings WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        if (!empty($row['google_authenticator_secret'])) {
            $secret = $row['google_authenticator_secret'];
            // $user_2fa_enabled = true; // We'll set this after successful verification or if already enabled
        }
    }
    $stmt->close();

    // Check if 2FA is marked as enabled
    $stmt_enabled_check = $db->prepare("SELECT google_2fa_enabled FROM user_security_settings WHERE user_id = ?");
    $stmt_enabled_check->bind_param("i", $user_id);
    $stmt_enabled_check->execute();
    $result_enabled_check = $stmt_enabled_check->get_result();
    if ($row_enabled = $result_enabled_check->fetch_assoc()) {
        if ($row_enabled['google_2fa_enabled'] == 1) {
            $user_2fa_enabled = true;
        }
    }
    $stmt_enabled_check->close();


} catch (Exception $e) {
    $feedback_message = "Error checking 2FA status: " . $e->getMessage();
    $feedback_type = 'danger';
}


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'generate_secret' && !$user_2fa_enabled) {
            $secret = $g->generateSecret();
            try {
                // Store the new secret temporarily or directly if preferred
                $stmt = $db->prepare("UPDATE user_security_settings SET google_authenticator_secret = ? WHERE user_id = ?");
                if (!$stmt) {
                    throw new Exception("DB Prepare failed: " . $db->error);
                }
                $stmt->bind_param("si", $secret, $user_id);
                if (!$stmt->execute()) {
                     throw new Exception("DB Execute failed: " . $stmt->error);
                }
                // If no rows affected, it means the user_id doesn't exist in user_security_settings
                if ($stmt->affected_rows === 0) {
                     // Attempt to insert a new record
                    $insertStmt = $db->prepare("INSERT INTO user_security_settings (user_id, google_authenticator_secret, otp_enabled, require_2fa, allow_remember_device, login_attempts_limit, lockout_duration, otp_expiry_minutes, created_by, updated_by) VALUES (?, ?, 0, 0, 1, 5, 15, 10, ?, ?)");
                    if (!$insertStmt) {
                        throw new Exception("DB Insert Prepare failed: " . $db->error);
                    }
                    $insertStmt->bind_param("isii", $user_id, $secret, $user_id, $user_id); // Assuming created_by and updated_by is the user themselves
                    if (!$insertStmt->execute()) {
                        throw new Exception("DB Insert Execute failed: " . $insertStmt->error);
                    }
                    $insertStmt->close();
                }
                $stmt->close();
                $feedback_message = "New secret generated. Please scan the QR code.";
                $feedback_type = 'info';
            } catch (Exception $e) {
                $feedback_message = "Error storing secret: " . $e->getMessage();
                $feedback_type = 'danger';
                $secret = null; // Clear secret on error
            }
        } elseif ($_POST['action'] === 'verify_code' && isset($_POST['otp_code']) && isset($_POST['secret_key'])) {
            $otp_code = trim($_POST['otp_code']);
            $submitted_secret = trim($_POST['secret_key']); // Use the secret from the form

            if (empty($otp_code) || empty($submitted_secret)) {
                $feedback_message = "OTP code and secret key are required.";
                $feedback_type = 'danger';
            } else {
                if ($g->checkCode($submitted_secret, $otp_code)) {
                    try {
                        $stmt = $db->prepare("UPDATE user_security_settings SET google_2fa_enabled = 1, google_authenticator_secret = ? WHERE user_id = ?");
                        $stmt->bind_param("si", $submitted_secret, $user_id);
                        $stmt->execute();
                        $stmt->close();
                        
                        $user_2fa_enabled = true;
                        $secret = $submitted_secret; // Ensure the displayed secret is the one verified
                        $feedback_message = "2FA enabled successfully!";
                        $feedback_type = 'success';
                    } catch (Exception $e) {
                        $feedback_message = "Error enabling 2FA: " . $e->getMessage();
                        $feedback_type = 'danger';
                    }
                } else {
                    $feedback_message = "Invalid OTP code. Please try again.";
                    $feedback_type = 'danger';
                    $secret = $submitted_secret; // Keep the secret for QR display
                }
            }
        } elseif ($_POST['action'] === 'disable_2fa') {
             try {
                $stmt = $db->prepare("UPDATE user_security_settings SET google_2fa_enabled = 0, google_authenticator_secret = NULL WHERE user_id = ?");
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                $stmt->close();
                
                $user_2fa_enabled = false;
                $secret = null;
                $qrCodeUrl = null;
                $feedback_message = "2FA disabled successfully.";
                $feedback_type = 'success';
            } catch (Exception $e) {
                $feedback_message = "Error disabling 2FA: " . $e->getMessage();
                $feedback_type = 'danger';
            }
        }
    }
}

// If a secret exists (either newly generated or from DB), create QR code URL
if ($secret && !$user_2fa_enabled) { // Only show QR if 2FA is not yet enabled or being re-configured
    // Fetch user email or username for the QR code label
    $user_identifier = 'UserEmailOrName'; // Fallback
    try {
        $stmt_user = $db->prepare("SELECT email, username FROM accounts WHERE id = ?");
        $stmt_user->bind_param("i", $user_id);
        $stmt_user->execute();
        $result_user = $stmt_user->get_result();
        if ($row_user = $result_user->fetch_assoc()) {
            $user_identifier = $row_user['email'] ?: $row_user['username'];
        }
        $stmt_user->close();
    } catch (Exception $e) {
        // Log error or handle, but continue with a generic identifier
    }
    $qrCodeUrl = GoogleQrUrl::generate($user_identifier, $secret, APP_NAME . ' 2FA');
}


include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="user-security-management.php">User Security</a></li>
        <li class="breadcrumb-item active" aria-current="page"><?php echo $page_title; ?></li>
    </ol>
</nav>

<?php if ($feedback_message): ?>
<div class="alert alert-<?php echo $feedback_type; ?> alert-dismissible" role="alert">
    <div class="d-flex">
        <div>
            <?php if ($feedback_type === 'success'): ?>
                <i class="fas fa-check-circle me-2"></i>
            <?php elseif ($feedback_type === 'danger'): ?>
                <i class="fas fa-times-circle me-2"></i>
            <?php else: ?>
                <i class="fas fa-info-circle me-2"></i>
            <?php endif; ?>
        </div>
        <div>
            <h4 class="alert-title"><?php echo ucfirst($feedback_type); ?>!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($feedback_message); ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<div class="row row-cards">
    <!-- 2FA Configuration Form -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-shield-alt me-2"></i>
                    Google Authenticator Setup
                </h3>
            </div>
            <div class="card-body">
                <?php if ($user_2fa_enabled): ?>
                    <div class="alert alert-success text-center">
                        <h4 class="alert-heading"><i class="fas fa-check-circle"></i> 2FA is Currently ACTIVE</h4>
                        <p>Google Authenticator is configured and active for this account.</p>
                    </div>
                    <form method="POST" action="">
                        <input type="hidden" name="action" value="disable_2fa">
                        <p class="text-center text-muted">If you wish to disable 2FA, or reconfigure it, click the button below. Disabling will remove your current secret key.</p>
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to disable 2FA? This will remove your current secret key.');">
                            <i class="fas fa-ban me-2"></i> Disable 2FA
                        </button>
                    </form>
                <?php else: ?>
                    <?php if (!$secret): ?>
                        <p class="text-center">Click the button below to generate a new secret key and QR code for Google Authenticator.</p>
                        <form method="POST" action="">
                            <input type="hidden" name="action" value="generate_secret">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-key me-2"></i> Generate Secret & QR Code
                            </button>
                        </form>
                    <?php endif; ?>

                    <?php if ($secret && $qrCodeUrl): ?>
                        <p class="text-center">
                            1. Scan the QR code below with your Google Authenticator app.
                            <br>
                            2. Enter the 6-digit code from the app to verify and enable 2FA.
                        </p>
                        <div class="text-center my-3">
                            <img src="<?php echo htmlspecialchars($qrCodeUrl); ?>" alt="QR Code for Google Authenticator">
                        </div>
                        <p class="text-center">
                            <strong>Secret Key:</strong> <code><?php echo htmlspecialchars($secret); ?></code>
                            <br><small class="text-muted">(If you cannot scan the QR code, you can manually enter this secret key into your authenticator app.)</small>
                        </p>

                        <hr>

                        <form method="POST" action="">
                            <input type="hidden" name="action" value="verify_code">
                            <input type="hidden" name="secret_key" value="<?php echo htmlspecialchars($secret); ?>">
                            <div class="mb-3">
                                <label for="otp_code" class="form-label">Authenticator Code <span class="text-danger">*</span></label>
                                <input type="text" id="otp_code" name="otp_code" class="form-control form-control-lg text-center" 
                                       placeholder="Enter 6-digit code" required maxlength="6" pattern="\d{6}"
                                       autocomplete="off">
                                <div class="form-text">Enter the code from your Google Authenticator app.</div>
                            </div>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-check-circle me-2"></i> Verify & Enable 2FA
                            </button>
                        </form>
                        
                        <hr>
                        <form method="POST" action="" class="mt-3">
                            <input type="hidden" name="action" value="generate_secret">
                             <p class="text-center text-muted">If you need to generate a new secret (e.g., if you lose access to your authenticator app before verifying), you can do so. This will invalidate the current QR code and secret.</p>
                            <button type="submit" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-redo me-2"></i> Regenerate Secret & QR Code
                            </button>
                        </form>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            <div class="card-footer text-center">
                <small class="text-muted">
                    Google Authenticator provides an extra layer of security for your account.
                </small>
            </div>
        </div>
    </div>

    <!-- Guidelines -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    2FA Setup Guide
                </h3>
            </div>
            <div class="card-body">
                <!-- Setup Steps -->
                <div class="alert alert-info mb-3">
                    <h4 class="alert-title">Setup Steps</h4>
                    <ol class="mb-0" style="font-size: 0.9rem;">
                        <li>Download Google Authenticator app</li>
                        <li>Generate secret key & QR code</li>
                        <li>Scan QR code with your app</li>
                        <li>Enter verification code</li>
                        <li>2FA is now active!</li>
                    </ol>
                </div>

                <!-- Security Benefits -->
                <div class="alert alert-success mb-3">
                    <h4 class="alert-title">Security Benefits</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Protection against password theft</li>
                        <li>Secure offline code generation</li>
                        <li>Works without internet connection</li>
                        <li>Industry-standard security</li>
                    </ul>
                </div>

                <!-- Important Notes -->
                <div class="alert alert-warning mb-3">
                    <h4 class="alert-title">Important Notes</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Keep backup codes safe</li>
                        <li>Don't lose your phone/app</li>
                        <li>Contact admin if locked out</li>
                        <li>Codes change every 30 seconds</li>
                    </ul>
                </div>

                <!-- App Download -->
                <div class="alert alert-secondary mb-0">
                    <h4 class="alert-title">Download App</h4>
                    <div style="font-size: 0.85rem;">
                        <strong>iOS:</strong> App Store → "Google Authenticator"<br>
                        <strong>Android:</strong> Play Store → "Google Authenticator"<br>
                        <strong>Alternative:</strong> Authy, Microsoft Authenticator
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
