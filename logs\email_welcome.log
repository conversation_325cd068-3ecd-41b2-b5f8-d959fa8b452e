=== EMAIL LOG ===
Date: 2025-06-01 15:59:51
To: <EMAIL>
Subject: Welcome to SecureBank Online - Your Account is Ready!
Type: welcome
Body (HTML):

    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to SecureBank Online</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">SecureBank Online</div>
                <h1>Welcome to Your New Account!</h1>
                <p>Your banking journey starts here</p>
            </div>

            <div class="content">
                <div class="welcome-card">
                    <h2>🎉 Congratulations, Demo!</h2>
                    <p>Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.</p>
                </div>

                <h3>📋 Your Account Details</h3>
                <div class="account-details">
                    <div class="detail-row">
                        <span class="detail-label">Account Holder:</span>
                        <span class="detail-value">Demo User</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Number:</span>
                        <span class="detail-value highlight">************</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Type:</span>
                        <span class="detail-value">Savings</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Currency:</span>
                        <span class="detail-value">USD</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Initial Balance:</span>
                        <span class="detail-value">$0.00</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Username:</span>
                        <span class="detail-value">demohome4042</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Email:</span>
                        <span class="detail-value"><EMAIL></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">✅ Active</span>
                    </div>
                </div>

                <div class="security-note">
                    <h4>🔐 Security Information</h4>
                    <ul>
                        <li>Your password has been set as provided during registration</li>
                        <li>Please log in and change your password if needed</li>
                        <li>Two-factor authentication (OTP) is enabled for your security</li>
                        <li>Never share your login credentials with anyone</li>
                    </ul>
                </div>

                <div style="text-align: center;">
                    <a href="http://localhost/online_banking/auth/login.php" class="button">🚀 Access Your Account</a>
                </div>

                <h3>🎯 What's Next?</h3>
                <ul>
                    <li>✅ <strong>Log in</strong> to your account using your username and password</li>
                    <li>✅ <strong>Complete your profile</strong> by adding additional information</li>
                    <li>✅ <strong>Explore features</strong> like transfers, virtual cards, and more</li>
                    <li>✅ <strong>Contact support</strong> if you need any assistance</li>
                </ul>

                <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>📞 Need Help?</h4>
                    <p>Our support team is here to help you get started:</p>
                    <ul>
                        <li>📧 Email: <EMAIL></li>
                        <li>🌐 Visit: http://localhost/online_banking/</li>
                        <li>💬 Live chat available 24/7</li>
                    </ul>
                </div>
            </div>

            <div class="footer">
                <p>Thank you for choosing SecureBank Online</p>
                <p>This email was <NAME_EMAIL></p>
                <p>&copy; 2025 SecureBank Online. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
Body (Text):

    
    
    
        
        
        Welcome to SecureBank Online
        
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        
    
    
        
            
                SecureBank Online
                Welcome to Your New Account!
                Your banking journey starts here
            

            
                
                    🎉 Congratulations, Demo!
                    Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.
                

                📋 Your Account Details
                
                    
                        Account Holder:
                        Demo User
                    
                    
                        Account Number:
                        ************
                    
                    
                        Account Type:
                        Savings
                    
                    
                        Currency:
                        USD
                    
                    
                        Initial Balance:
                        $0.00
                    
                    
                        Username:
                        demohome4042
                    
                    
                        Email:
                        <EMAIL>
                    
                    
                        Status:
                        ✅ Active
                    
                

                
                    🔐 Security Information
                    
                        Your password has been set as provided during registration
                        Please log in and change your password if needed
                        Two-factor authentication (OTP) is enabled for your security
                        Never share your login credentials with anyone
                    
                

                
                    🚀 Access Your Account
                

                🎯 What's Next?
                
                    ✅ Log in to your account using your username and password
                    ✅ Complete your profile by adding additional information
                    ✅ Explore features like transfers, virtual cards, and more
                    ✅ Contact support if you need any assistance
                

                
                    📞 Need Help?
                    Our support team is here to help you get started:
                    
                        📧 Email: <EMAIL>
                        🌐 Visit: http://localhost/online_banking/
                        💬 Live chat available 24/7
                    
                
            

            
                Thank you for choosing SecureBank Online
                This email was <NAME_EMAIL>
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
==================

