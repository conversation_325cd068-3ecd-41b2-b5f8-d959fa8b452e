<?php
require_once '../config/config.php';

// Require admin authentication
requireAdmin();

$page_title = 'Admin Dashboard';

// Get system statistics
try {
    $db = getDB();
    
    // Total users
    $total_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0");
    $total_users = $total_users_result->fetch_assoc()['count'];
    
    // Active users
    $active_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND status = 'active'");
    $active_users = $active_users_result->fetch_assoc()['count'];
    
    // Suspended users
    $suspended_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND status = 'suspended'");
    $suspended_users = $suspended_users_result->fetch_assoc()['count'];
    
    // Total transactions today
    $today_transactions_result = $db->query("SELECT COUNT(*) as count FROM transfers WHERE DATE(created_at) = CURDATE()");
    $today_transactions = $today_transactions_result->fetch_assoc()['count'];
    
    // Total transaction volume today
    $today_volume_result = $db->query("SELECT SUM(amount) as volume FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed'");
    $today_volume = $today_volume_result->fetch_assoc()['volume'] ?? 0;
    
    // Pending KYC verifications
    $pending_kyc_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE kyc_status = 'pending'");
    $pending_kyc = $pending_kyc_result->fetch_assoc()['count'];
    
    // Recent user registrations (last 7 days)
    $recent_users_result = $db->query("
        SELECT id, username, first_name, last_name, email, created_at, status 
        FROM accounts 
        WHERE is_admin = 0 AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    
    // Recent transactions (last 10)
    $recent_transactions_result = $db->query("
        SELECT t.*, 
               sender.username as sender_username,
               recipient.username as recipient_username
        FROM transfers t
        LEFT JOIN accounts sender ON t.sender_id = sender.id
        LEFT JOIN accounts recipient ON t.recipient_id = recipient.id
        ORDER BY t.created_at DESC 
        LIMIT 10
    ");
    
    // Open support tickets
    $open_tickets_result = $db->query("SELECT COUNT(*) as count FROM tickets WHERE status IN ('open', 'in_progress')");
    $open_tickets = $open_tickets_result->fetch_assoc()['count'];
    
} catch (Exception $e) {
    error_log("Admin dashboard error: " . $e->getMessage());
    $total_users = $active_users = $suspended_users = $today_transactions = $today_volume = $pending_kyc = $open_tickets = 0;
    $recent_users_result = $recent_transactions_result = null;
}

include 'includes/admin-header.php';
?>

<!-- Quick Action Button in Top Bar -->
<div style="position: absolute; top: 1rem; right: 2rem;">
    <a href="users.php" class="btn btn-primary">
        <i class="fas fa-users me-2"></i>Manage Users
    </a>
</div>
<!-- System Statistics -->
<div class="row row-deck row-cards mb-3">
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Total Users</div>
                                <div class="ms-auto lh-1">
                                    <div class="dropdown">
                                        <a class="dropdown-toggle text-muted" href="users.php">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <circle cx="9" cy="7" r="4"/>
                                                <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                                <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3"><?php echo number_format($total_users); ?></div>
                            <div class="d-flex mb-2">
                                <div>Registered accounts</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Active Users</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-green">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="9"/>
                                            <polyline points="9 12 12 15 15 9"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3"><?php echo number_format($active_users); ?></div>
                            <div class="d-flex mb-2">
                                <div>Currently active</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Today's Transactions</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-blue">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M7 18a4.6 4.4 0 0 1 0 -9a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12"/>
                                            <polyline points="9 15 12 12 15 15"/>
                                            <polyline points="12 12 12 21"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3"><?php echo number_format($today_transactions); ?></div>
                            <div class="d-flex mb-2">
                                <div><?php echo formatCurrency($today_volume); ?> volume</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Pending KYC</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="9"/>
                                            <polyline points="12 7 12 12 15 15"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3"><?php echo number_format($pending_kyc); ?></div>
                            <div class="d-flex mb-2">
                                <div>Awaiting verification</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row row-deck row-cards mb-3">
                <div class="col-md-6 col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Quick Actions</h3>
                        </div>
                        <div class="card-body">
                            <div class="divide-y">
                                <div class="row">
                                    <div class="col-auto">
                                        <span class="avatar bg-blue-lt">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <line x1="12" y1="5" x2="12" y2="19"/>
                                                <line x1="5" y1="12" x2="19" y2="12"/>
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="col">
                                        <div class="text-truncate">
                                            <a href="add-user.php" class="text-body d-block">Add New User</a>
                                            <div class="d-block text-muted text-truncate mt-n1">
                                                Create a new user account
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="divide-y">
                                <div class="row">
                                    <div class="col-auto">
                                        <span class="avatar bg-green-lt">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <circle cx="9" cy="7" r="4"/>
                                                <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                                <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/>
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="col">
                                        <div class="text-truncate">
                                            <a href="users.php" class="text-body d-block">Manage Users</a>
                                            <div class="d-block text-muted text-truncate mt-n1">
                                                View and edit user accounts
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="divide-y">
                                <div class="row">
                                    <div class="col-auto">
                                        <span class="avatar bg-yellow-lt">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M7 18a4.6 4.4 0 0 1 0 -9a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12"/>
                                                <polyline points="9 15 12 12 15 15"/>
                                                <polyline points="12 12 12 21"/>
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="col">
                                        <div class="text-truncate">
                                            <a href="transactions.php" class="text-body d-block">View Transactions</a>
                                            <div class="d-block text-muted text-truncate mt-n1">
                                                Monitor all system transactions
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Users -->
                <div class="col-md-6 col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Recent User Registrations</h3>
                            <div class="card-actions">
                                <a href="users.php" class="btn btn-outline-primary btn-sm">
                                    View All
                                </a>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <?php if ($recent_users_result && $recent_users_result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-vcenter card-table">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Email</th>
                                            <th>Status</th>
                                            <th>Registered</th>
                                            <th class="w-1"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($user = $recent_users_result->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex py-1 align-items-center">
                                                    <span class="avatar avatar-sm me-2"><?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?></span>
                                                    <div class="flex-fill">
                                                        <div class="font-weight-medium"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                                        <div class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="text-muted">
                                                <?php echo htmlspecialchars($user['email']); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['status'] === 'active' ? 'green' : ($user['status'] === 'suspended' ? 'red' : 'yellow'); ?>-lt">
                                                    <?php echo ucfirst($user['status']); ?>
                                                </span>
                                            </td>
                                            <td class="text-muted">
                                                <?php echo formatDate($user['created_at'], 'M d, Y'); ?>
                                            </td>
                                            <td>
                                                <div class="btn-list flex-nowrap">
                                                    <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-white btn-sm">
                                                        Edit
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="empty">
                                <div class="empty-img">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik02NCA2NlY1NCIgc3Ryb2tlPSIjREFEREUyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNNjQgNzhWNzYiIHN0cm9rZT0iI0RBRERFRSI+PHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="" height="128" width="128">
                                </div>
                                <p class="empty-title">No recent registrations</p>
                                <p class="empty-subtitle text-muted">
                                    New user registrations will appear here.
                                </p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
