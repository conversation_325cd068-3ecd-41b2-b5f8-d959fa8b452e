<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'User Management';

// Handle search and filters
$search = sanitizeInput($_GET['search'] ?? '');
$status_filter = sanitizeInput($_GET['status'] ?? '');
$kyc_filter = sanitizeInput($_GET['kyc'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query conditions
$conditions = ["is_admin = 0"];
$params = [];

if (!empty($search)) {
    $conditions[] = "(username LIKE ? OR first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR account_number LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

if (!empty($status_filter)) {
    $conditions[] = "status = ?";
    $params[] = $status_filter;
}

if (!empty($kyc_filter)) {
    $conditions[] = "kyc_status = ?";
    $params[] = $kyc_filter;
}

$where_clause = "WHERE " . implode(" AND ", $conditions);

try {
    $db = getDB();
    
    // Get total count for pagination
    $count_sql = "SELECT COUNT(*) as total FROM accounts $where_clause";
    $count_result = $db->query($count_sql, $params);
    $total_users = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_users / $per_page);
    
    // Get users for current page with OTP information
    $sql = "SELECT a.id, a.account_number, a.username, a.first_name, a.last_name, a.email, a.phone,
                   a.account_type, a.balance, a.status, a.kyc_status, a.created_at, a.last_login,
                   o.otp_code, o.expires_at as otp_expires, o.source as otp_source
            FROM accounts a
            LEFT JOIN user_otps o ON a.id = o.user_id AND o.expires_at > NOW() AND o.used = 0
            $where_clause
            ORDER BY a.created_at DESC
            LIMIT ? OFFSET ?";

    $params[] = $per_page;
    $params[] = $offset;

    $users_result = $db->query($sql, $params);
    
} catch (Exception $e) {
    error_log("Users page error: " . $e->getMessage());
    $users_result = null;
    $total_users = 0;
    $total_pages = 1;
}

include 'includes/admin-header.php';
?>

<!-- Add User Button in Top Bar -->
<div style="position: absolute; top: 1rem; right: 2rem;">
    <a href="add-user.php" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Add User
    </a>
</div>
<!-- Search and Filters -->
<div class="row row-cards mb-3">
    <div class="col-12">
        <div class="card">
                        <div class="card-body">
                            <form method="GET" action="">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label">Search Users</label>
                                        <input type="text" name="search" class="form-control" placeholder="Username, name, email, account..." value="<?php echo htmlspecialchars($search); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Status</label>
                                        <select name="status" class="form-select">
                                            <option value="">All Statuses</option>
                                            <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                            <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">KYC Status</label>
                                        <select name="kyc" class="form-select">
                                            <option value="">All KYC</option>
                                            <option value="pending" <?php echo $kyc_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                            <option value="verified" <?php echo $kyc_filter === 'verified' ? 'selected' : ''; ?>>Verified</option>
                                            <option value="rejected" <?php echo $kyc_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="btn-list">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                    <circle cx="11" cy="11" r="8"/>
                                                    <path d="M21 21l-4.35-4.35"/>
                                                </svg>
                                                Search
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Users Table -->
            <div class="row row-cards">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Users (<?php echo number_format($total_users); ?> total)</h3>
                        </div>
                        <div class="card-body p-0">
                            <?php if ($users_result && $users_result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-vcenter card-table">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Account</th>
                                            <th>Contact</th>
                                            <th>Balance</th>
                                            <th>Status</th>
                                            <th>KYC</th>
                                            <th>OTP</th>
                                            <th>Joined</th>
                                            <th class="w-1"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($user = $users_result->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex py-1 align-items-center">
                                                    <span class="avatar avatar-sm me-2"><?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?></span>
                                                    <div class="flex-fill">
                                                        <div class="font-weight-medium"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                                        <div class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="font-weight-medium"><?php echo htmlspecialchars($user['account_number']); ?></div>
                                                    <div class="text-muted"><?php echo ucfirst($user['account_type']); ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div><?php echo htmlspecialchars($user['email']); ?></div>
                                                    <?php if (!empty($user['phone'])): ?>
                                                    <div class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="text-end">
                                                <div class="font-weight-medium"><?php echo formatCurrency($user['balance']); ?></div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['status'] === 'active' ? 'green' : ($user['status'] === 'suspended' ? 'red' : 'gray'); ?>-lt">
                                                    <?php echo ucfirst($user['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['kyc_status'] === 'verified' ? 'green' : ($user['kyc_status'] === 'rejected' ? 'red' : 'yellow'); ?>-lt">
                                                    <?php echo ucfirst($user['kyc_status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (!empty($user['otp_code'])): ?>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-<?php echo $user['otp_source'] === 'admin' ? 'warning' : 'primary'; ?> font-monospace me-2">
                                                        <?php echo htmlspecialchars($user['otp_code']); ?>
                                                    </span>
                                                    <div class="text-muted small">
                                                        <div>
                                                            <i class="fas fa-<?php echo $user['otp_source'] === 'admin' ? 'user-shield' : 'envelope'; ?> me-1"></i>
                                                            <?php echo ucfirst($user['otp_source'] ?? 'login'); ?>
                                                        </div>
                                                        <div>
                                                            <i class="fas fa-clock me-1"></i>
                                                            <?php echo formatDate($user['otp_expires'], 'H:i'); ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php else: ?>
                                                <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-muted">
                                                <?php echo formatDate($user['created_at'], 'M d, Y'); ?>
                                            </td>
                                            <td>
                                                <div class="btn-list flex-nowrap">
                                                    <a href="view-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-secondary" title="Edit User">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($user['status'] === 'active'): ?>
                                                    <button class="btn btn-sm btn-outline-info" title="Generate OTP" onclick="generateOTPQuick(<?php echo $user['id']; ?>, this)">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                    <a href="suspend-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-warning" title="Suspend User" onclick="return confirm('Are you sure you want to suspend this user?')">
                                                        <i class="fas fa-ban"></i>
                                                    </a>
                                                    <?php else: ?>
                                                    <a href="activate-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-success" title="Activate User" onclick="return confirm('Are you sure you want to activate this user?')">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                    <?php endif; ?>
                                                    <button class="btn btn-sm btn-outline-danger" title="Delete User" onclick="showDeleteConfirmation(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($user['email'], ENT_QUOTES); ?>', this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                            <div class="card-footer d-flex align-items-center">
                                <p class="m-0 text-muted">
                                    Showing <span><?php echo $offset + 1; ?></span> to <span><?php echo min($offset + $per_page, $total_users); ?></span> of <span><?php echo $total_users; ?></span> entries
                                </p>
                                <ul class="pagination m-0 ms-auto">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <polyline points="15 18 9 12 15 6"/>
                                            </svg>
                                            prev
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            next
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <polyline points="9 6 15 12 9 18"/>
                                            </svg>
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <?php endif; ?>
                            
                            <?php else: ?>
                            <div class="empty">
                                <div class="empty-img">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik02NCA2NlY1NCIgc3Ryb2tlPSIjREFEREUyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNNjQgNzhWNzYiIHN0cm9rZT0iI0RBRERFRSI+PHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="" height="128" width="128">
                                </div>
                                <p class="empty-title">No users found</p>
                                <p class="empty-subtitle text-muted">
                                    Try adjusting your search or filter criteria.
                                </p>
                                <div class="empty-action">
                                    <a href="add-user.php" class="btn btn-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <line x1="12" y1="5" x2="12" y2="19"/>
                                            <line x1="5" y1="12" x2="19" y2="12"/>
                                        </svg>
                                        Add New User
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Beautiful Delete Confirmation Modal -->
<div id="deleteConfirmModal" class="modal-overlay" style="display: none;">
    <div class="modal-content confirm-modal">
        <div class="modal-header">
            <div class="d-flex align-items-center">
                <div class="warning-icon me-3">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h4 class="mb-0">Confirm Delete Action</h4>
            </div>
            <button type="button" class="modal-close" onclick="hideDeleteConfirmation()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="modal-body">
            <!-- User Info Card -->
            <div class="user-preview-card">
                <div class="user-avatar">
                    <span id="confirmUserInitials">JS</span>
                </div>
                <div class="user-details">
                    <h5 id="confirmUserName">Jane Smith</h5>
                    <p id="confirmUserEmail"><EMAIL></p>
                </div>
            </div>

            <!-- Main Question -->
            <div class="confirmation-question">
                <h5>Are you sure you want to perform this action?</h5>
                <p>This will permanently delete the user and all associated data including:</p>
            </div>

            <!-- Data List -->
            <div class="deletion-list">
                <div class="deletion-item">
                    <i class="fas fa-user"></i>
                    <span>User account and profile information</span>
                </div>
                <div class="deletion-item">
                    <i class="fas fa-credit-card"></i>
                    <span>Transaction history and financial records</span>
                </div>
                <div class="deletion-item">
                    <i class="fas fa-wallet"></i>
                    <span>Virtual cards and payment methods</span>
                </div>
                <div class="deletion-item">
                    <i class="fas fa-ticket-alt"></i>
                    <span>Support tickets and communications</span>
                </div>
                <div class="deletion-item">
                    <i class="fas fa-shield-alt"></i>
                    <span>Login sessions and security logs</span>
                </div>
            </div>

            <!-- Final Warning -->
            <div class="final-warning">
                <i class="fas fa-exclamation-circle"></i>
                <strong>This action cannot be undone!</strong>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-cancel" onclick="hideDeleteConfirmation()">
                <i class="fas fa-times me-2"></i>Cancel
            </button>
            <button type="button" class="btn btn-delete" id="confirmDeleteBtn" onclick="executeDelete()">
                <i class="fas fa-trash me-2"></i>Yes, Delete User
            </button>
        </div>
    </div>
</div>

<style>
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-content.confirm-modal {
    max-width: 480px;
}

.modal-header {
    padding: 24px 24px 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.warning-icon {
    width: 48px;
    height: 48px;
    background: #fff3cd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #856404;
    font-size: 20px;
}

.modal-header h4 {
    color: #212529;
    font-weight: 600;
}

.modal-close {
    background: #f8f9fa;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    cursor: pointer;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #e9ecef;
    color: #495057;
}

.modal-body {
    padding: 16px 24px 24px 24px;
}

.user-preview-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
    flex-shrink: 0;
}

.user-details h5 {
    margin: 0 0 4px 0;
    color: #212529;
    font-weight: 600;
    font-size: 16px;
}

.user-details p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

.confirmation-question {
    margin-bottom: 20px;
}

.confirmation-question h5 {
    color: #212529;
    font-weight: 600;
    margin-bottom: 8px;
}

.confirmation-question p {
    color: #6c757d;
    margin: 0;
}

.deletion-list {
    background: #fff5f5;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 4px solid #dc3545;
}

.deletion-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    color: #721c24;
}

.deletion-item i {
    width: 16px;
    color: #dc3545;
    font-size: 14px;
}

.deletion-item span {
    font-size: 14px;
}

.final-warning {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #721c24;
    font-size: 14px;
}

.final-warning i {
    color: #dc3545;
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: #f8f9fa;
}

.btn-cancel {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-cancel:hover {
    background: #5a6268;
}

.btn-delete {
    background: #dc3545;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-delete:hover {
    background: #c82333;
}
</style>

<script>
function generateOTPQuick(userId, button) {
    const originalHTML = button.innerHTML;
    const row = button.closest('tr');
    const otpCell = row.querySelector('td:nth-child(7)'); // OTP column

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Make AJAX request
    fetch(`generate-otp.php?id=${userId}&ajax=1`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the OTP cell
                otpCell.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary font-monospace me-2">${data.otp}</span>
                        <div class="text-muted small">
                            <i class="fas fa-clock me-1"></i>
                            ${data.expires.split(' ')[3]} <!-- Show only time -->
                        </div>
                    </div>
                `;

                // Show success notification
                showQuickNotification('success', `OTP generated: ${data.otp}`);

                // Reset button after 2 seconds
                setTimeout(() => {
                    button.disabled = false;
                    button.innerHTML = originalHTML;
                }, 2000);
            } else {
                // Show error notification
                showQuickNotification('error', data.message);

                // Reset button
                button.disabled = false;
                button.innerHTML = originalHTML;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showQuickNotification('error', 'An error occurred while generating OTP.');

            // Reset button
            button.disabled = false;
            button.innerHTML = originalHTML;
        });
}

// Simple delete function
function simpleDeleteUser(userId, username, button) {
    // Simple confirmation dialog
    if (!confirm(`Are you sure you want to delete user "${username}"?\n\nThis action cannot be undone.`)) {
        return;
    }

    // Show loading state
    const originalHTML = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Make delete request
    fetch(`delete-user.php?id=${userId}&ajax=1`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove row from table
            const row = button.closest('tr');
            if (row) {
                row.style.transition = 'opacity 0.3s ease';
                row.style.opacity = '0';
                setTimeout(() => {
                    row.remove();
                    updateUserCount(-1);
                }, 300);
            }
            showQuickNotification('success', `User "${username}" has been deleted successfully`);
        } else {
            showQuickNotification('error', data.message || 'Failed to delete user');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        showQuickNotification('error', 'An error occurred while deleting the user');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalHTML;
    });
}

function updateUserCount(change) {
    const userCountElement = document.querySelector('h3');
    if (userCountElement && userCountElement.textContent.includes('total')) {
        const currentCount = parseInt(userCountElement.textContent.match(/\d+/)[0]);
        userCountElement.textContent = `Users (${Math.max(0, currentCount + change)} total)`;
    }
}

function showQuickNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';

    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 4000);
}

// Global variables for delete confirmation
let deleteUserData = null;
let deleteButtonRef = null;

// Show beautiful confirmation modal
function showDeleteConfirmation(userId, username, email, button) {
    deleteUserData = { userId, username, email };
    deleteButtonRef = button;

    // Set user info in modal
    document.getElementById('confirmUserName').textContent = username;
    document.getElementById('confirmUserEmail').textContent = email;

    // Generate initials for avatar
    const initials = username.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
    document.getElementById('confirmUserInitials').textContent = initials;

    // Show modal
    document.getElementById('deleteConfirmModal').style.display = 'flex';
}

// Hide confirmation modal
function hideDeleteConfirmation() {
    document.getElementById('deleteConfirmModal').style.display = 'none';
    deleteUserData = null;
    deleteButtonRef = null;
}

// Execute the delete action
function executeDelete() {
    if (!deleteUserData || !deleteButtonRef) return;

    const { userId, username } = deleteUserData;
    const button = deleteButtonRef;

    // Hide modal first
    hideDeleteConfirmation();

    // Show loading state
    const originalHTML = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Make delete request
    fetch(`delete-user.php?id=${userId}&ajax=1`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove row from table
            const row = button.closest('tr');
            if (row) {
                row.style.transition = 'opacity 0.3s ease';
                row.style.opacity = '0';
                setTimeout(() => {
                    row.remove();
                    updateUserCount(-1);
                }, 300);
            }
            showQuickNotification('success', `User "${username}" has been deleted successfully`);
        } else {
            showQuickNotification('error', data.message || 'Failed to delete user');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        showQuickNotification('error', 'An error occurred while deleting the user');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalHTML;
    });
}

// Close modal on escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && document.getElementById('deleteConfirmModal').style.display === 'flex') {
        hideDeleteConfirmation();
    }
});
</script>

<?php include 'includes/admin-footer.php'; ?>
