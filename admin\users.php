<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'User Management';

// Handle search and filters
$search = sanitizeInput($_GET['search'] ?? '');
$status_filter = sanitizeInput($_GET['status'] ?? '');
$kyc_filter = sanitizeInput($_GET['kyc'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query conditions
$conditions = ["is_admin = 0"];
$params = [];

if (!empty($search)) {
    $conditions[] = "(username LIKE ? OR first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR account_number LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

if (!empty($status_filter)) {
    $conditions[] = "status = ?";
    $params[] = $status_filter;
}

if (!empty($kyc_filter)) {
    $conditions[] = "kyc_status = ?";
    $params[] = $kyc_filter;
}

$where_clause = "WHERE " . implode(" AND ", $conditions);

try {
    $db = getDB();
    
    // Get total count for pagination
    $count_sql = "SELECT COUNT(*) as total FROM accounts $where_clause";
    $count_result = $db->query($count_sql, $params);
    $total_users = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_users / $per_page);
    
    // Get users for current page with OTP information
    $sql = "SELECT a.id, a.account_number, a.username, a.first_name, a.last_name, a.email, a.phone,
                   a.account_type, a.balance, a.status, a.kyc_status, a.created_at, a.last_login,
                   o.otp_code, o.expires_at as otp_expires, o.source as otp_source
            FROM accounts a
            LEFT JOIN user_otps o ON a.id = o.user_id AND o.expires_at > NOW() AND o.used = 0
            $where_clause
            ORDER BY a.created_at DESC
            LIMIT ? OFFSET ?";

    $params[] = $per_page;
    $params[] = $offset;

    $users_result = $db->query($sql, $params);
    
} catch (Exception $e) {
    error_log("Users page error: " . $e->getMessage());
    $users_result = null;
    $total_users = 0;
    $total_pages = 1;
}

include '../includes/admin_header.php';
?>



<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        User Management
                    </h2>
                </div>
                <div class="col-auto ms-auto d-print-none">
                    <div class="btn-list">
                        <a href="add-user.php" class="btn btn-primary d-none d-sm-inline-block">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <line x1="12" y1="5" x2="12" y2="19"/>
                                <line x1="5" y1="12" x2="19" y2="12"/>
                            </svg>
                            Add User
                        </a>
                        <a href="add-user.php" class="btn btn-primary d-sm-none btn-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <line x1="12" y1="5" x2="12" y2="19"/>
                                <line x1="5" y1="12" x2="19" y2="12"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <!-- Search and Filters -->
            <div class="row row-cards mb-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" action="">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label">Search Users</label>
                                        <input type="text" name="search" class="form-control" placeholder="Username, name, email, account..." value="<?php echo htmlspecialchars($search); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Status</label>
                                        <select name="status" class="form-select">
                                            <option value="">All Statuses</option>
                                            <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                            <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">KYC Status</label>
                                        <select name="kyc" class="form-select">
                                            <option value="">All KYC</option>
                                            <option value="pending" <?php echo $kyc_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                            <option value="verified" <?php echo $kyc_filter === 'verified' ? 'selected' : ''; ?>>Verified</option>
                                            <option value="rejected" <?php echo $kyc_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="btn-list">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                    <circle cx="11" cy="11" r="8"/>
                                                    <path d="M21 21l-4.35-4.35"/>
                                                </svg>
                                                Search
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Users Table -->
            <div class="row row-cards">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Users (<?php echo number_format($total_users); ?> total)</h3>
                        </div>
                        <div class="card-body p-0">
                            <?php if ($users_result && $users_result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-vcenter card-table">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Account</th>
                                            <th>Contact</th>
                                            <th>Balance</th>
                                            <th>Status</th>
                                            <th>KYC</th>
                                            <th>OTP</th>
                                            <th>Joined</th>
                                            <th class="w-1"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($user = $users_result->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex py-1 align-items-center">
                                                    <span class="avatar avatar-sm me-2"><?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?></span>
                                                    <div class="flex-fill">
                                                        <div class="font-weight-medium"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                                        <div class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="font-weight-medium"><?php echo htmlspecialchars($user['account_number']); ?></div>
                                                    <div class="text-muted"><?php echo ucfirst($user['account_type']); ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div><?php echo htmlspecialchars($user['email']); ?></div>
                                                    <?php if (!empty($user['phone'])): ?>
                                                    <div class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="text-end">
                                                <div class="font-weight-medium"><?php echo formatCurrency($user['balance']); ?></div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['status'] === 'active' ? 'green' : ($user['status'] === 'suspended' ? 'red' : 'gray'); ?>-lt">
                                                    <?php echo ucfirst($user['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['kyc_status'] === 'verified' ? 'green' : ($user['kyc_status'] === 'rejected' ? 'red' : 'yellow'); ?>-lt">
                                                    <?php echo ucfirst($user['kyc_status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (!empty($user['otp_code'])): ?>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-<?php echo $user['otp_source'] === 'admin' ? 'warning' : 'primary'; ?> font-monospace me-2">
                                                        <?php echo htmlspecialchars($user['otp_code']); ?>
                                                    </span>
                                                    <div class="text-muted small">
                                                        <div>
                                                            <i class="fas fa-<?php echo $user['otp_source'] === 'admin' ? 'user-shield' : 'envelope'; ?> me-1"></i>
                                                            <?php echo ucfirst($user['otp_source'] ?? 'login'); ?>
                                                        </div>
                                                        <div>
                                                            <i class="fas fa-clock me-1"></i>
                                                            <?php echo formatDate($user['otp_expires'], 'H:i'); ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php else: ?>
                                                <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-muted">
                                                <?php echo formatDate($user['created_at'], 'M d, Y'); ?>
                                            </td>
                                            <td>
                                                <div class="btn-list flex-nowrap">
                                                    <a href="view-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-secondary" title="Edit User">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($user['status'] === 'active'): ?>
                                                    <button class="btn btn-sm btn-outline-info" title="Generate OTP" onclick="generateOTPQuick(<?php echo $user['id']; ?>, this)">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                    <a href="suspend-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-warning" title="Suspend User" onclick="return confirm('Are you sure you want to suspend this user?')">
                                                        <i class="fas fa-ban"></i>
                                                    </a>
                                                    <?php else: ?>
                                                    <a href="activate-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-success" title="Activate User" onclick="return confirm('Are you sure you want to activate this user?')">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                    <?php endif; ?>
                                                    <button class="btn btn-sm btn-outline-danger" title="Delete User" onclick="showDeleteModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($user['email'], ENT_QUOTES); ?>', this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                            <div class="card-footer d-flex align-items-center">
                                <p class="m-0 text-muted">
                                    Showing <span><?php echo $offset + 1; ?></span> to <span><?php echo min($offset + $per_page, $total_users); ?></span> of <span><?php echo $total_users; ?></span> entries
                                </p>
                                <ul class="pagination m-0 ms-auto">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <polyline points="15 18 9 12 15 6"/>
                                            </svg>
                                            prev
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            next
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <polyline points="9 6 15 12 9 18"/>
                                            </svg>
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <?php endif; ?>
                            
                            <?php else: ?>
                            <div class="empty">
                                <div class="empty-img">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik02NCA2NlY1NCIgc3Ryb2tlPSIjREFEREUyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNNjQgNzhWNzYiIHN0cm9rZT0iI0RBRERFRSI+PHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="" height="128" width="128">
                                </div>
                                <p class="empty-title">No users found</p>
                                <p class="empty-subtitle text-muted">
                                    Try adjusting your search or filter criteria.
                                </p>
                                <div class="empty-action">
                                    <a href="add-user.php" class="btn btn-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <line x1="12" y1="5" x2="12" y2="19"/>
                                            <line x1="5" y1="12" x2="19" y2="12"/>
                                        </svg>
                                        Add New User
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Secure Delete User Modal -->
<div id="deleteModal" class="modal-overlay" style="display: none;">
    <div class="modal-content delete-modal">
        <div class="modal-header">
            <h3><i class="fas fa-user-times text-warning me-2"></i>User Management Options</h3>
            <button type="button" class="modal-close" onclick="hideDeleteModal()">&times;</button>
        </div>
        <div class="modal-body">
            <!-- User Info -->
            <div class="user-info-card">
                <div class="d-flex align-items-center mb-3">
                    <div class="user-avatar me-3">
                        <span id="deleteUserInitials"></span>
                    </div>
                    <div>
                        <h5 class="mb-1" id="deleteUserFullName"></h5>
                        <div class="text-muted">@<span id="deleteUsername"></span></div>
                        <div class="text-muted small" id="deleteEmail"></div>
                    </div>
                </div>
                <div id="userDataSummary" class="user-data-summary"></div>
            </div>

            <!-- Security Warning -->
            <div class="alert alert-warning mb-4">
                <i class="fas fa-shield-alt me-2"></i>
                <strong>Security Notice:</strong> Choose the appropriate action based on your compliance requirements.
            </div>

            <!-- Action Options -->
            <div class="action-options">
                <div class="action-card recommended" onclick="selectAction('soft_delete')">
                    <div class="action-header">
                        <input type="radio" name="deleteAction" value="soft_delete" id="action_soft_delete">
                        <label for="action_soft_delete">
                            <i class="fas fa-user-slash text-warning"></i>
                            <span class="action-title">Disable Account</span>
                            <span class="badge bg-success ms-2">Recommended</span>
                        </label>
                    </div>
                    <div class="action-description">
                        Deactivate the user account but preserve all data for auditing and compliance. User cannot log in but all transaction history remains intact.
                    </div>
                </div>

                <div class="action-card" onclick="selectAction('archive')">
                    <div class="action-header">
                        <input type="radio" name="deleteAction" value="archive" id="action_archive">
                        <label for="action_archive">
                            <i class="fas fa-archive text-info"></i>
                            <span class="action-title">Archive User</span>
                        </label>
                    </div>
                    <div class="action-description">
                        Move user to archive table and disable account. Preserves key information while marking the account as archived.
                    </div>
                </div>

                <div class="action-card danger" onclick="selectAction('hard_delete')">
                    <div class="action-header">
                        <input type="radio" name="deleteAction" value="hard_delete" id="action_hard_delete">
                        <label for="action_hard_delete">
                            <i class="fas fa-trash text-danger"></i>
                            <span class="action-title">Permanent Deletion</span>
                            <span class="badge bg-danger ms-2">High Risk</span>
                        </label>
                    </div>
                    <div class="action-description">
                        <strong class="text-danger">⚠️ IRREVERSIBLE:</strong> Permanently delete all user data. Requires additional verification. Use only when absolutely necessary.
                    </div>
                </div>
            </div>

            <!-- Reason Input -->
            <div class="reason-section">
                <label for="deleteReason" class="form-label">Reason for action (required):</label>
                <textarea class="form-control" id="deleteReason" rows="3" placeholder="Provide a detailed reason for this action..."></textarea>
            </div>

            <!-- Confirmation Code Section (for hard delete) -->
            <div id="confirmationSection" class="confirmation-section" style="display: none;">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Final Verification Required</strong>
                </div>
                <div class="d-flex gap-2 mb-3">
                    <button type="button" class="btn btn-outline-primary" onclick="generateConfirmationCode()">
                        <i class="fas fa-key me-1"></i>Generate Code
                    </button>
                    <input type="text" class="form-control" id="confirmationCode" placeholder="Enter confirmation code" maxlength="8">
                </div>
                <div class="small text-muted">
                    Click "Generate Code" to receive a verification code, then enter it above to confirm permanent deletion.
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="hideDeleteModal()">Cancel</button>
            <button type="button" class="btn btn-primary" id="confirmActionBtn" onclick="confirmAction()" disabled>
                <i class="fas fa-check me-1"></i>Execute Action
            </button>
        </div>
    </div>
</div>

<style>
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content.delete-modal {
    max-width: 700px;
}

.modal-header {
    padding: 20px 20px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #dc3545;
}

.modal-body {
    padding: 0 20px 20px 20px;
}

.user-info-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 18px;
}

.user-data-summary {
    background: white;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}

.data-metric {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.data-metric:last-child {
    border-bottom: none;
}

.action-options {
    margin-bottom: 20px;
}

.action-card {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-card:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.action-card.selected {
    border-color: #007bff;
    background-color: #e7f1ff;
}

.action-card.recommended {
    border-color: #28a745;
}

.action-card.recommended:hover,
.action-card.recommended.selected {
    border-color: #28a745;
    background-color: #d4edda;
}

.action-card.danger {
    border-color: #ffc107;
}

.action-card.danger:hover,
.action-card.danger.selected {
    border-color: #dc3545;
    background-color: #f8d7da;
}

.action-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.action-header input[type="radio"] {
    margin-right: 10px;
}

.action-header label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    width: 100%;
}

.action-header label i {
    margin-right: 8px;
    font-size: 16px;
    width: 20px;
}

.action-title {
    margin-right: 8px;
}

.action-description {
    color: #6c757d;
    font-size: 0.9em;
    line-height: 1.4;
    margin-left: 30px;
}

.reason-section {
    margin-bottom: 20px;
}

.reason-section label {
    font-weight: 500;
    margin-bottom: 8px;
}

.confirmation-section {
    border: 2px solid #dc3545;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background-color: #fff5f5;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.modal-footer .btn {
    min-width: 120px;
}

.info-row {
    margin-bottom: 8px;
}

.info-row:last-child {
    margin-bottom: 0;
}
</style>

<script>
function generateOTPQuick(userId, button) {
    const originalHTML = button.innerHTML;
    const row = button.closest('tr');
    const otpCell = row.querySelector('td:nth-child(7)'); // OTP column

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Make AJAX request
    fetch(`generate-otp.php?id=${userId}&ajax=1`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the OTP cell
                otpCell.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary font-monospace me-2">${data.otp}</span>
                        <div class="text-muted small">
                            <i class="fas fa-clock me-1"></i>
                            ${data.expires.split(' ')[3]} <!-- Show only time -->
                        </div>
                    </div>
                `;

                // Show success notification
                showQuickNotification('success', `OTP generated: ${data.otp}`);

                // Reset button after 2 seconds
                setTimeout(() => {
                    button.disabled = false;
                    button.innerHTML = originalHTML;
                }, 2000);
            } else {
                // Show error notification
                showQuickNotification('error', data.message);

                // Reset button
                button.disabled = false;
                button.innerHTML = originalHTML;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showQuickNotification('error', 'An error occurred while generating OTP.');

            // Reset button
            button.disabled = false;
            button.innerHTML = originalHTML;
        });
}

// Modal delete system with preview
let deleteUserId = null;
let deleteUserButton = null;

function showDeleteModal(userId, username, email, button) {
    deleteUserId = userId;
    deleteUserButton = button;

    // Get user data from the row
    const row = button.closest('tr');
    const nameCell = row.querySelector('td:first-child');
    const fullName = nameCell.querySelector('.font-weight-medium').textContent;
    const balanceCell = row.querySelector('td:nth-child(4)');
    const balance = balanceCell.textContent.trim();

    // Set user info in modal
    document.getElementById('deleteUsername').textContent = username;
    document.getElementById('deleteEmail').textContent = email;
    document.getElementById('deleteUserFullName').textContent = fullName;

    // Generate initials for avatar
    const initials = fullName.split(' ').map(n => n[0]).join('').toUpperCase();
    document.getElementById('deleteUserInitials').textContent = initials;

    // Show user data summary
    document.getElementById('userDataSummary').innerHTML = `
        <div class="row g-2">
            <div class="col-6"><div class="data-metric"><span>Account Balance:</span><strong>${balance}</strong></div></div>
            <div class="col-6"><div class="data-metric"><span>Username:</span><strong>${username}</strong></div></div>
            <div class="col-12"><div class="data-metric"><span>Email:</span><strong>${email}</strong></div></div>
        </div>
        <div class="alert alert-warning mt-3">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Warning:</strong> This will permanently delete the user and ALL associated data including:
            <ul class="mb-0 mt-2">
                <li>Account information and balance</li>
                <li>Transaction history</li>
                <li>Virtual cards</li>
                <li>Support tickets</li>
                <li>Login sessions</li>
                <li>All related records</li>
            </ul>
        </div>
    `;

    // Reset and show modal
    resetDeleteForm();
    document.getElementById('deleteModal').style.display = 'flex';
}

function resetDeleteForm() {
    // Select hard delete by default
    document.getElementById('action_hard_delete').checked = true;
    selectAction('hard_delete');

    // Set default reason
    document.getElementById('deleteReason').value = 'Administrative deletion - complete user removal';

    // Update button state
    updateConfirmButton();
}

function selectAction(action) {
    // Update confirm button for hard delete
    const confirmButton = document.getElementById('confirmActionBtn');
    confirmButton.innerHTML = '<i class="fas fa-trash me-1"></i>Delete User Permanently';
    confirmButton.className = 'btn btn-danger';
    confirmButton.disabled = false;
}

function updateConfirmButton() {
    const confirmButton = document.getElementById('confirmActionBtn');
    confirmButton.disabled = false;
}

function confirmAction() {
    if (!deleteUserId) return;

    const reason = document.getElementById('deleteReason').value.trim();

    // Final confirmation
    if (!confirm('⚠️ FINAL WARNING: This will permanently delete ALL user data and cannot be undone. Are you absolutely sure?')) {
        return;
    }

    const button = document.getElementById('confirmActionBtn');
    const originalHTML = button.innerHTML;

    // Show loading
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';

    // Make delete request
    fetch(`delete-user.php?id=${deleteUserId}&ajax=1`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            hideDeleteModal();

            // Remove row from table
            if (deleteUserButton) {
                const row = deleteUserButton.closest('tr');
                if (row) {
                    row.style.transition = 'opacity 0.3s ease';
                    row.style.opacity = '0';
                    setTimeout(() => {
                        row.remove();
                        updateUserCount(-1);
                    }, 300);
                }
            }
            showQuickNotification('success', 'User and all associated data have been permanently deleted');
        } else {
            showQuickNotification('error', data.message || 'Failed to delete user');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        showQuickNotification('error', 'An error occurred while deleting the user');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalHTML;
    });
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
    deleteUserId = null;
    deleteUserButton = null;
}

function updateUserCount(change) {
    const userCountElement = document.querySelector('h3');
    if (userCountElement && userCountElement.textContent.includes('total')) {
        const currentCount = parseInt(userCountElement.textContent.match(/\d+/)[0]);
        userCountElement.textContent = `Users (${Math.max(0, currentCount + change)} total)`;
    }
}

function showQuickNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';

    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 4000);
}

// Simple delete system - no complex modals needed
</script>

<?php include '../includes/footer.php'; ?>
