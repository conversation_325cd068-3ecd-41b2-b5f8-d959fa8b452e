<?php
require_once '../config/config.php';

// Check if user is admin
requireAdmin();

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['id']) && isset($_GET['ajax'])) {
    $user_id = (int)$_GET['id'];
    
    if ($user_id <= 0) {
        $response['message'] = 'Invalid user ID.';
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    
    try {
        $db = getDB();
        
        // First, get user details for logging
        $user_query = "SELECT id, username, email, first_name, last_name, account_number FROM accounts WHERE id = ? AND is_admin = 0";
        $user_result = $db->query($user_query, [$user_id]);
        
        if ($user_result->num_rows === 0) {
            $response['message'] = 'User not found or cannot delete admin users.';
            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }
        
        $user = $user_result->fetch_assoc();
        
        // Prevent deletion of admin users
        if ($user['id'] == $_SESSION['user_id']) {
            $response['message'] = 'You cannot delete your own account.';
            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }
        
        // Start transaction
        $db->query("START TRANSACTION");
        
        try {
            // Delete related records first (to maintain referential integrity)

            // Delete OTP records (only if table exists)
            try {
                $db->query("DELETE FROM user_otps WHERE user_id = ?", [$user_id]);
            } catch (Exception $e) {
                // Table might not exist, continue
            }

            // Delete transaction history (only if table exists)
            try {
                $db->query("DELETE FROM transactions WHERE account_id = ?", [$user_id]);
            } catch (Exception $e) {
                // Table might not exist, continue
            }

            // Delete virtual cards (only if table exists)
            try {
                $db->query("DELETE FROM virtual_cards WHERE user_id = ?", [$user_id]);
            } catch (Exception $e) {
                // Table might not exist, continue
            }

            // Delete user sessions (only if table exists)
            try {
                $db->query("DELETE FROM user_sessions WHERE user_id = ?", [$user_id]);
            } catch (Exception $e) {
                // Table might not exist, continue
            }

            // Finally, delete the user account
            $affected_rows = $db->delete("DELETE FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);

            if ($affected_rows > 0) {
                // Commit transaction
                $db->query("COMMIT");
                
                // Log the deletion (optional - create admin_logs table if needed)
                $admin_id = $_SESSION['user_id'];
                $log_message = "Admin deleted user: {$user['username']} ({$user['email']}) - Account: {$user['account_number']}";
                
                // You can add logging to a separate admin_logs table here
                // $db->query("INSERT INTO admin_logs (admin_id, action, details, created_at) VALUES (?, 'DELETE_USER', ?, NOW())", 
                //           [$admin_id, $log_message]);
                
                $response['success'] = true;
                $response['message'] = "User '{$user['username']}' has been permanently deleted.";
            } else {
                // Rollback transaction
                $db->query("ROLLBACK");
                $response['message'] = 'Failed to delete user. User may not exist.';
            }
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $db->query("ROLLBACK");
            throw $e;
        }
        
    } catch (Exception $e) {
        error_log("Delete user error: " . $e->getMessage());
        $response['message'] = 'An error occurred while deleting the user: ' . $e->getMessage();
    }
    
} else {
    $response['message'] = 'Invalid request method or missing parameters.';
}

// Return JSON response for AJAX requests
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// For non-AJAX requests, redirect back to users page
if ($response['success']) {
    $_SESSION['success'] = $response['message'];
} else {
    $_SESSION['error'] = $response['message'];
}

header('Location: users.php');
exit;
?>
