<!DOCTYPE html>
<html>
<head>
    <title>Test Delete User</title>
</head>
<body>
    <h1>Test Delete User Functionality</h1>
    <button onclick="testDeleteUser()">Test Delete User ID 8</button>
    <div id="result"></div>

    <script>
    function testDeleteUser() {
        const userId = 8; // demohome4042 user
        
        fetch(`../admin/delete-user.php?id=${userId}&ajax=1`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('result').innerHTML = 
                '<h3>Result:</h3>' +
                '<p><strong>Success:</strong> ' + data.success + '</p>' +
                '<p><strong>Message:</strong> ' + data.message + '</p>' +
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('result').innerHTML = 
                '<h3>Error:</h3>' +
                '<p>' + error.message + '</p>';
        });
    }
    </script>
</body>
</html>
