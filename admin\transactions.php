<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'All Transactions';

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_type = $_GET['type'] ?? '';
$filter_status = $_GET['status'] ?? '';
$filter_user = $_GET['user'] ?? '';
$filter_date_from = $_GET['date_from'] ?? '';
$filter_date_to = $_GET['date_to'] ?? '';
$filter_amount_min = floatval($_GET['amount_min'] ?? 0);
$filter_amount_max = floatval($_GET['amount_max'] ?? 0);

// Build WHERE clause for filters
$where_conditions = [];
$params = [];

if (!empty($filter_type)) {
    $where_conditions[] = "at.transaction_type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_status)) {
    $where_conditions[] = "at.status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_user)) {
    $where_conditions[] = "(a.first_name LIKE ? OR a.last_name LIKE ? OR a.username LIKE ?)";
    $search_term = "%$filter_user%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "DATE(at.created_at) >= ?";
    $params[] = $filter_date_from;
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "DATE(at.created_at) <= ?";
    $params[] = $filter_date_to;
}

if ($filter_amount_min > 0) {
    $where_conditions[] = "at.amount >= ?";
    $params[] = $filter_amount_min;
}

if ($filter_amount_max > 0) {
    $where_conditions[] = "at.amount <= ?";
    $params[] = $filter_amount_max;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();

    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total
                    FROM account_transactions at
                    LEFT JOIN accounts a ON at.account_id = a.id
                    $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);

    // Get transactions with pagination
    $transactions_query = "SELECT at.*,
                          a.first_name, a.last_name, a.username, a.account_number,
                          admin.first_name as admin_first_name, admin.last_name as admin_last_name
                          FROM account_transactions at
                          LEFT JOIN accounts a ON at.account_id = a.id
                          LEFT JOIN accounts admin ON at.processed_by = admin.id
                          $where_clause
                          ORDER BY at.created_at DESC
                          LIMIT $records_per_page OFFSET $offset";

    $transactions_result = $db->query($transactions_query, $params);
    $transactions = [];
    while ($row = $transactions_result->fetch_assoc()) {
        $transactions[] = $row;
    }

    // Get users for filter dropdown
    $users_result = $db->query("SELECT id, first_name, last_name, username FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name");
    $users = [];
    while ($user = $users_result->fetch_assoc()) {
        $users[] = $user;
    }

} catch (Exception $e) {
    $error = "Failed to load transactions: " . $e->getMessage();
    $transactions = [];
    $total_records = 0;
    $total_pages = 0;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">All Transactions</li>
    </ol>
</nav>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Filters Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Transaction Filters
                </h3>
                <div class="card-actions">
                    <a href="transactions.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-3">
                    <!-- Transaction Type Filter -->
                    <div class="col-md-2">
                        <label class="form-label">Type</label>
                        <select name="type" class="form-select">
                            <option value="">All Types</option>
                            <option value="credit" <?php echo $filter_type === 'credit' ? 'selected' : ''; ?>>Credit</option>
                            <option value="debit" <?php echo $filter_type === 'debit' ? 'selected' : ''; ?>>Debit</option>
                            <option value="transfer_in" <?php echo $filter_type === 'transfer_in' ? 'selected' : ''; ?>>Transfer In</option>
                            <option value="transfer_out" <?php echo $filter_type === 'transfer_out' ? 'selected' : ''; ?>>Transfer Out</option>
                            <option value="deposit" <?php echo $filter_type === 'deposit' ? 'selected' : ''; ?>>Deposit</option>
                            <option value="withdrawal" <?php echo $filter_type === 'withdrawal' ? 'selected' : ''; ?>>Withdrawal</option>
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="processing" <?php echo $filter_status === 'processing' ? 'selected' : ''; ?>>Processing</option>
                            <option value="completed" <?php echo $filter_status === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="cancelled" <?php echo $filter_status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            <option value="failed" <?php echo $filter_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                        </select>
                    </div>

                    <!-- User Search -->
                    <div class="col-md-2">
                        <label class="form-label">User</label>
                        <input type="text" name="user" class="form-control" placeholder="Search user..." value="<?php echo htmlspecialchars($filter_user); ?>">
                    </div>

                    <!-- Date From -->
                    <div class="col-md-2">
                        <label class="form-label">Date From</label>
                        <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                    </div>

                    <!-- Date To -->
                    <div class="col-md-2">
                        <label class="form-label">Date To</label>
                        <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                    </div>

                    <!-- Amount Range -->
                    <div class="col-md-1">
                        <label class="form-label">Min Amount</label>
                        <input type="number" name="amount_min" class="form-control" step="0.01" placeholder="0.00" value="<?php echo $filter_amount_min > 0 ? $filter_amount_min : ''; ?>">
                    </div>

                    <div class="col-md-1">
                        <label class="form-label">Max Amount</label>
                        <input type="number" name="amount_max" class="form-control" step="0.01" placeholder="0.00" value="<?php echo $filter_amount_max > 0 ? $filter_amount_max : ''; ?>">
                    </div>

                    <!-- Filter Button -->
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            Apply Filters
                        </button>
                        <a href="credit-debit.php" class="btn btn-success ms-2">
                            <i class="fas fa-plus me-2"></i>
                            New Transaction
                        </a>
                        <button type="button" class="btn btn-outline-primary ms-2" onclick="exportTransactions()">
                            <i class="fas fa-download me-2"></i>
                            Export CSV
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-list me-2"></i>
                    All Transactions
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> total</span>
                </h3>
                <div class="card-actions">
                    <span class="text-muted">
                        Showing <?php echo number_format(($current_page - 1) * $records_per_page + 1); ?> -
                        <?php echo number_format(min($current_page * $records_per_page, $total_records)); ?>
                        of <?php echo number_format($total_records); ?> transactions
                    </span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($transactions)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter table-mobile-md card-table">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>Transaction Details</th>
                                <th>User</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Processed By</th>
                                <th>Date</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($transactions as $transaction):
                            ?>
                            <tr>
                                <!-- Row Number -->
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <!-- Transaction Details -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold">ID: <?php echo $transaction['id']; ?></div>
                                        <small class="text-muted">Ref: <?php echo htmlspecialchars($transaction['reference_number']); ?></small>
                                        <small class="text-muted">Category: <?php echo htmlspecialchars($transaction['category']); ?></small>
                                    </div>
                                </td>

                                <!-- User -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2">
                                            <?php echo strtoupper(substr($transaction['first_name'] ?? 'U', 0, 1) . substr($transaction['last_name'] ?? 'U', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars(($transaction['first_name'] ?? 'Unknown') . ' ' . ($transaction['last_name'] ?? 'User')); ?></div>
                                            <div class="text-muted">@<?php echo htmlspecialchars($transaction['username'] ?? 'unknown'); ?></div>
                                            <small class="text-muted">Acc: <?php echo htmlspecialchars($transaction['account_number'] ?? 'N/A'); ?></small>
                                        </div>
                                    </div>
                                </td>

                                <!-- Type -->
                                <td>
                                    <?php
                                    $type_colors = [
                                        'credit' => 'success',
                                        'debit' => 'danger',
                                        'transfer_in' => 'info',
                                        'transfer_out' => 'warning',
                                        'deposit' => 'primary',
                                        'withdrawal' => 'secondary'
                                    ];
                                    $color = $type_colors[$transaction['transaction_type']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $color; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $transaction['transaction_type'])); ?>
                                    </span>
                                </td>

                                <!-- Amount -->
                                <td>
                                    <span class="fw-bold <?php echo in_array($transaction['transaction_type'], ['credit', 'deposit', 'transfer_in']) ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo in_array($transaction['transaction_type'], ['credit', 'deposit', 'transfer_in']) ? '+' : '-'; ?>
                                        <?php echo formatCurrency($transaction['amount'], $transaction['currency']); ?>
                                    </span>
                                </td>

                                <!-- Description -->
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($transaction['description']); ?>">
                                        <?php echo htmlspecialchars($transaction['description']); ?>
                                    </div>
                                </td>

                                <!-- Status -->
                                <td>
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'completed' => 'success',
                                        'cancelled' => 'secondary',
                                        'failed' => 'danger'
                                    ];
                                    $status_color = $status_colors[$transaction['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?>">
                                        <?php echo ucfirst($transaction['status']); ?>
                                    </span>
                                </td>

                                <!-- Processed By -->
                                <td>
                                    <?php if ($transaction['admin_first_name']): ?>
                                    <div class="text-muted">
                                        <?php echo htmlspecialchars($transaction['admin_first_name'] . ' ' . $transaction['admin_last_name']); ?>
                                    </div>
                                    <?php else: ?>
                                    <span class="text-muted">System</span>
                                    <?php endif; ?>
                                </td>

                                <!-- Date -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($transaction['created_at'])); ?></small>
                                        <?php if ($transaction['updated_at'] !== $transaction['created_at']): ?>
                                        <small class="text-warning">Updated: <?php echo date('M j, g:i A', strtotime($transaction['updated_at'])); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>

                                <!-- Actions -->
                                <td>
                                    <div class="btn-list">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewTransaction(<?php echo $transaction['id']; ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if (in_array($transaction['status'], ['pending', 'processing'])): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="updateStatus(<?php echo $transaction['id']; ?>, 'completed')" title="Mark Completed">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="updateStatus(<?php echo $transaction['id']; ?>, 'cancelled')" title="Cancel">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-search" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No transactions found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty(array_filter([$filter_type, $filter_status, $filter_user, $filter_date_from, $filter_date_to]))): ?>
                        Try adjusting your filters to see more results.
                        <?php else: ?>
                        No transactions have been recorded yet.
                        <?php endif; ?>
                    </p>
                    <div class="empty-action">
                        <a href="credit-debit.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Create First Transaction
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Pagination -->
<?php if ($total_pages > 1): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted">
                        Page <?php echo $current_page; ?> of <?php echo $total_pages; ?>
                        (<?php echo number_format($total_records); ?> total transactions)
                    </div>
                    <nav aria-label="Transaction pagination">
                        <ul class="pagination mb-0">
                            <!-- First Page -->
                            <?php if ($current_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- Previous Page -->
                            <?php if ($current_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page - 1])); ?>">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- Page Numbers -->
                            <?php
                            $start_page = max(1, $current_page - 2);
                            $end_page = min($total_pages, $current_page + 2);

                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                            <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>

                            <!-- Next Page -->
                            <?php if ($current_page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page + 1])); ?>">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- Last Page -->
                            <?php if ($current_page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Transaction management functions
function viewTransaction(transactionId) {
    // Create modal or redirect to transaction details
    alert('View transaction details for ID: ' + transactionId + '\n\nThis would open a detailed view of the transaction.');
}

function updateStatus(transactionId, newStatus) {
    if (!confirm('Are you sure you want to change the transaction status to "' + newStatus + '"?')) {
        return;
    }

    fetch('update-transaction-status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            transaction_id: transactionId,
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Transaction status updated successfully', 'success');
            // Reload page to show updated status
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification('Failed to update transaction status: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error updating transaction status:', error);
        showNotification('Error updating transaction status', 'error');
    });
}

function exportTransactions() {
    // Get current filter parameters
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');

    // Create download link
    const downloadUrl = 'export-transactions.php?' + params.toString();

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = 'transactions_' + new Date().toISOString().split('T')[0] + '.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('Export started. Download will begin shortly.', 'info');
}

// Notification system
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' :
                     type === 'error' ? 'alert-danger' :
                     type === 'info' ? 'alert-info' : 'alert-warning';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Auto-submit form when filters change (optional)
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form');
    const autoSubmitElements = filterForm.querySelectorAll('select[name="type"], select[name="status"]');

    autoSubmitElements.forEach(element => {
        element.addEventListener('change', function() {
            // Optional: Auto-submit on filter change
            // filterForm.submit();
        });
    });
});
</script>

<?php include 'includes/admin-footer.php'; ?>
