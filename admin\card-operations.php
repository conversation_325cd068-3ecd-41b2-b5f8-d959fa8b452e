<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Virtual Card Operations';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        $card_id = intval($_POST['card_id']);
        $operation_type = $_POST['operation_type'];
        $amount = floatval($_POST['amount']);
        $description = trim($_POST['description']);
        $transaction_date = $_POST['transaction_date'] ?? date('Y-m-d H:i:s');
        
        // Validate inputs
        if (empty($card_id) || empty($operation_type) || $amount <= 0 || empty($description)) {
            throw new Exception("All fields are required and amount must be greater than zero.");
        }
        
        // Get card details
        $card_query = "SELECT vc.*, a.first_name, a.last_name, a.username 
                       FROM virtual_cards vc 
                       LEFT JOIN accounts a ON vc.account_id = a.id 
                       WHERE vc.card_id = ?";
        $card_result = $db->query($card_query, [$card_id]);
        $card = $card_result->fetch_assoc();
        
        if (!$card) {
            throw new Exception("Virtual card not found.");
        }
        
        if ($card['status'] !== 'active') {
            throw new Exception("Cannot perform operations on inactive cards.");
        }
        
        // Validate debit operations
        if ($operation_type === 'debit' && $card['card_balance'] < $amount) {
            throw new Exception("Insufficient card balance. Current balance: " . formatCurrency($card['card_balance']));
        }
        
        // Start transaction
        $db->query("START TRANSACTION");
        
        // Calculate new balance
        $new_balance = $operation_type === 'credit' 
            ? $card['card_balance'] + $amount 
            : $card['card_balance'] - $amount;
        
        // Update card balance
        $update_balance = "UPDATE virtual_cards SET card_balance = ?, updated_at = NOW() WHERE card_id = ?";
        $balance_result = $db->query($update_balance, [$new_balance, $card_id]);
        
        if (!$balance_result) {
            throw new Exception("Failed to update card balance.");
        }
        
        // Generate reference number
        $reference = 'VCOP' . date('Ymd') . str_pad($card_id, 4, '0', STR_PAD_LEFT) . rand(100, 999);
        
        // Insert transaction record
        $insert_transaction = "INSERT INTO virtual_card_transactions (
            card_id, account_id, transaction_type, amount, currency, description, 
            reference_number, status, processed_by, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'completed', ?, ?)";
        
        $trans_result = $db->query($insert_transaction, [
            $card_id, $card['account_id'], $operation_type, $amount, $card['currency'],
            $description, $reference, $_SESSION['user_id'], $transaction_date
        ]);
        
        if (!$trans_result) {
            throw new Exception("Failed to record transaction.");
        }
        
        // Commit transaction
        $db->query("COMMIT");
        
        $success = ucfirst($operation_type) . " operation completed successfully! New balance: " . formatCurrency($new_balance);
        
    } catch (Exception $e) {
        if (isset($db)) {
            $db->query("ROLLBACK");
        }
        $error = $e->getMessage();
    }
}

// Get active virtual cards for dropdown
try {
    $db = getDB();
    $cards_query = "SELECT vc.card_id, vc.card_number, vc.card_holder_name, vc.card_balance, vc.currency,
                    a.first_name, a.last_name, a.username, a.account_number
                    FROM virtual_cards vc 
                    LEFT JOIN accounts a ON vc.account_id = a.id 
                    WHERE vc.status = 'active'
                    ORDER BY a.first_name, a.last_name";
    $cards_result = $db->query($cards_query);
    $cards = [];
    while ($card = $cards_result->fetch_assoc()) {
        $cards[] = $card;
    }
    
    // Get recent card transactions
    $recent_query = "SELECT vct.*, vc.card_number, vc.card_holder_name,
                     a.first_name, a.last_name, a.username,
                     admin.first_name as admin_first_name, admin.last_name as admin_last_name
                     FROM virtual_card_transactions vct
                     LEFT JOIN virtual_cards vc ON vct.card_id = vc.card_id
                     LEFT JOIN accounts a ON vct.account_id = a.id
                     LEFT JOIN accounts admin ON vct.processed_by = admin.id
                     ORDER BY vct.created_at DESC 
                     LIMIT 10";
    $recent_result = $db->query($recent_query);
    $recent_transactions = [];
    while ($transaction = $recent_result->fetch_assoc()) {
        $recent_transactions[] = $transaction;
    }
    
} catch (Exception $e) {
    $cards = [];
    $recent_transactions = [];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="virtual-cards.php">Virtual Cards</a></li>
        <li class="breadcrumb-item active" aria-current="page">Card Operations</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row row-cards">
    <!-- Card Operations Form -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Virtual Card Operations
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="mb-3">
                        <label class="form-label">Select Virtual Card <span class="text-danger">*</span></label>
                        <select name="card_id" class="form-select" required onchange="updateCardInfo()">
                            <option value="">Choose virtual card...</option>
                            <?php foreach ($cards as $card): ?>
                            <option value="<?php echo $card['card_id']; ?>" 
                                    data-balance="<?php echo $card['card_balance']; ?>"
                                    data-currency="<?php echo $card['currency']; ?>"
                                    data-holder="<?php echo htmlspecialchars($card['card_holder_name']); ?>"
                                    data-number="<?php echo substr($card['card_number'], -4); ?>"
                                    <?php echo (isset($_POST['card_id']) && $_POST['card_id'] == $card['card_id']) ? 'selected' : ''; ?>>
                                **** **** **** <?php echo substr($card['card_number'], -4); ?> - 
                                <?php echo htmlspecialchars($card['card_holder_name']); ?> 
                                (Balance: <?php echo formatCurrency($card['card_balance'], $card['currency']); ?>)
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <div id="cardInfo" class="mt-2" style="display: none;">
                            <small class="text-muted">
                                <strong>Current Balance:</strong> <span id="currentBalance"></span><br>
                                <strong>Card Holder:</strong> <span id="cardHolder"></span>
                            </small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Operation Type <span class="text-danger">*</span></label>
                                <select name="operation_type" class="form-select" required>
                                    <option value="">Select operation...</option>
                                    <option value="credit" <?php echo (isset($_POST['operation_type']) && $_POST['operation_type'] === 'credit') ? 'selected' : ''; ?>>
                                        Credit (Add Funds)
                                    </option>
                                    <option value="debit" <?php echo (isset($_POST['operation_type']) && $_POST['operation_type'] === 'debit') ? 'selected' : ''; ?>>
                                        Debit (Deduct Funds)
                                    </option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="amount" class="form-control" 
                                           step="0.01" min="0.01" max="100000" 
                                           placeholder="0.00" 
                                           value="<?php echo $_POST['amount'] ?? ''; ?>" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea name="description" class="form-control" rows="3" 
                                  placeholder="Enter operation description..." required><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        <small class="form-hint">Provide a clear description for this operation</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Transaction Date</label>
                        <input type="datetime-local" name="transaction_date" class="form-control" 
                               value="<?php echo $_POST['transaction_date'] ?? date('Y-m-d\TH:i'); ?>">
                        <small class="form-hint">Leave as current time or select a different date/time</small>
                    </div>
                    
                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check me-2"></i>
                            Process Operation
                        </button>
                        <a href="virtual-cards.php" class="btn btn-secondary ms-2">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Cards
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Quick Guidelines -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Operation Guidelines
                </h3>
            </div>
            <div class="card-body">
                <!-- Operation Types -->
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="card card-sm bg-success-lt">
                            <div class="card-body text-center">
                                <i class="fas fa-plus-circle text-success mb-2" style="font-size: 1.5rem;"></i>
                                <h4 class="card-title">Credit</h4>
                                <p class="text-muted mb-0">Add funds to card balance</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card card-sm bg-danger-lt">
                            <div class="card-body text-center">
                                <i class="fas fa-minus-circle text-danger mb-2" style="font-size: 1.5rem;"></i>
                                <h4 class="card-title">Debit</h4>
                                <p class="text-muted mb-0">Deduct funds from card</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Key Rules -->
                <div class="alert alert-info mb-3">
                    <h4 class="alert-title">Essential Rules</h4>
                    <div class="row">
                        <div class="col-6">
                            <ul class="mb-0" style="font-size: 0.9rem;">
                                <li>Verify card status</li>
                                <li>Check balance for debits</li>
                                <li>Use clear descriptions</li>
                            </ul>
                        </div>
                        <div class="col-6">
                            <ul class="mb-0" style="font-size: 0.9rem;">
                                <li>Confirm authorization</li>
                                <li>Document all operations</li>
                                <li>Monitor for fraud</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Description Examples -->
                <div class="alert alert-secondary mb-0">
                    <h4 class="alert-title">Description Examples</h4>
                    <div style="font-size: 0.85rem;">
                        <strong>Credit:</strong> "Customer top-up via bank transfer"<br>
                        <strong>Debit:</strong> "Manual adjustment - Customer request"<br>
                        <strong>Refund:</strong> "Merchant refund - Transaction #12345"
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Card Transactions -->
<?php if (!empty($recent_transactions)): ?>
<div class="row row-cards mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history me-2"></i>
                    Recent Card Transactions
                </h3>
                <div class="card-actions">
                    <a href="card-transactions.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-list me-2"></i>
                        View All Transactions
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th>Card</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Processed By</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_transactions as $transaction): ?>
                            <tr>
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold">**** <?php echo substr($transaction['card_number'], -4); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($transaction['card_holder_name']); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $type_colors = [
                                        'credit' => 'success',
                                        'debit' => 'danger',
                                        'purchase' => 'info',
                                        'refund' => 'warning'
                                    ];
                                    $color = $type_colors[$transaction['transaction_type']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $color; ?> badge-sm">
                                        <?php echo ucfirst($transaction['transaction_type']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="fw-bold <?php echo in_array($transaction['transaction_type'], ['credit', 'refund']) ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo in_array($transaction['transaction_type'], ['credit', 'refund']) ? '+' : '-'; ?>
                                        <?php echo formatCurrency($transaction['amount'], $transaction['currency']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($transaction['description']); ?>">
                                        <?php echo htmlspecialchars($transaction['description']); ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($transaction['admin_first_name']): ?>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($transaction['admin_first_name'] . ' ' . $transaction['admin_last_name']); ?>
                                    </small>
                                    <?php else: ?>
                                    <small class="text-muted">System</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <small class="text-muted"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></small>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($transaction['created_at'])); ?></small>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function updateCardInfo() {
    const select = document.querySelector('select[name="card_id"]');
    const selectedOption = select.options[select.selectedIndex];
    const cardInfo = document.getElementById('cardInfo');

    if (selectedOption.value) {
        const balance = selectedOption.getAttribute('data-balance');
        const currency = selectedOption.getAttribute('data-currency');
        const holder = selectedOption.getAttribute('data-holder');

        document.getElementById('currentBalance').textContent =
            new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency || 'USD'
            }).format(balance);
        document.getElementById('cardHolder').textContent = holder;

        cardInfo.style.display = 'block';
    } else {
        cardInfo.style.display = 'none';
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const operationType = document.querySelector('select[name="operation_type"]').value;
    const amount = parseFloat(document.querySelector('input[name="amount"]').value) || 0;
    const selectedCard = document.querySelector('select[name="card_id"]');
    const selectedOption = selectedCard.options[selectedCard.selectedIndex];

    if (operationType === 'debit' && selectedOption.value) {
        const currentBalance = parseFloat(selectedOption.getAttribute('data-balance')) || 0;

        if (amount > currentBalance) {
            e.preventDefault();
            alert('Insufficient card balance. Current balance: $' + currentBalance.toFixed(2));
            return false;
        }
    }
});

// Auto-update card info on page load if card is pre-selected
document.addEventListener('DOMContentLoaded', function() {
    updateCardInfo();
});
</script>

<?php include 'includes/admin-footer.php'; ?>
