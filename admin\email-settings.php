<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Email Settings Management';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_settings') {
        try {
            $db = getDB();
            
            $smtp_host = trim($_POST['smtp_host']);
            $smtp_port = intval($_POST['smtp_port']);
            $smtp_username = trim($_POST['smtp_username']);
            $smtp_password = trim($_POST['smtp_password']);
            $smtp_encryption = $_POST['smtp_encryption'];
            $sender_name = trim($_POST['sender_name']);
            $sender_email = trim($_POST['sender_email']);
            $mail_method = $_POST['mail_method'];
            
            // Validate inputs
            if (empty($smtp_host) || empty($smtp_username) || empty($sender_name) || empty($sender_email)) {
                throw new Exception("Required fields cannot be empty.");
            }
            
            if (!filter_var($sender_email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception("Invalid sender email address.");
            }
            
            if ($smtp_port < 1 || $smtp_port > 65535) {
                throw new Exception("Invalid SMTP port number.");
            }
            
            // Encrypt password (simple base64 for demo - use proper encryption in production)
            $encrypted_password = !empty($smtp_password) ? base64_encode($smtp_password) : '';
            
            // Deactivate current active settings
            $deactivate_current = "UPDATE email_settings SET is_active = 0 WHERE is_active = 1";
            $db->query($deactivate_current);
            
            // Insert new settings
            $insert_settings = "INSERT INTO email_settings (
                smtp_host, smtp_port, smtp_username, smtp_password, smtp_encryption,
                sender_name, sender_email, mail_method, created_by, updated_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $settings_id = $db->insert($insert_settings, [
                $smtp_host, $smtp_port, $smtp_username, $encrypted_password, $smtp_encryption,
                $sender_name, $sender_email, $mail_method, $_SESSION['user_id'], $_SESSION['user_id']
            ]);
            
            if ($settings_id) {
                $success = "✅ Email settings updated successfully!";
            } else {
                throw new Exception("Failed to update email settings.");
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    } elseif (isset($_POST['action']) && $_POST['action'] === 'test_email') {
        try {
            $db = getDB();
            
            $test_email = trim($_POST['test_email']);
            $test_method = $_POST['test_method'];
            
            if (empty($test_email) || !filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception("Please provide a valid email address for testing.");
            }
            
            // Load email settings
            $settings_query = "SELECT * FROM email_settings WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1";
            $settings_result = $db->query($settings_query);
            $email_settings = $settings_result->fetch_assoc();
            
            if (!$email_settings) {
                throw new Exception("No email settings found. Please configure email settings first.");
            }
            
            // Include PHPMailer
            require_once '../vendor/autoload.php';
            
            // Send test email
            $result = sendTestEmail($test_email, $email_settings, $test_method);
            
            if ($result['success']) {
                $success = "✅ Test email sent successfully to " . $test_email . " using " . strtoupper($test_method) . " method!";
                
                // Log the test email
                $log_email = "INSERT INTO email_logs (
                    recipient_email, subject, email_type, method_used, status, sent_by
                ) VALUES (?, ?, ?, ?, ?, ?)";
                $db->query($log_email, [
                    $test_email, 'Email System Test', 'test', $test_method, 'sent', $_SESSION['user_id']
                ]);
            } else {
                throw new Exception("Failed to send test email: " . $result['error']);
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
            
            // Log the failed test email
            if (isset($test_email) && isset($test_method)) {
                $log_email = "INSERT INTO email_logs (
                    recipient_email, subject, email_type, method_used, status, error_message, sent_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $db->query($log_email, [
                    $test_email, 'Email System Test', 'test', $test_method, 'failed', $e->getMessage(), $_SESSION['user_id']
                ]);
            }
        }
    }
}

// Get current active email settings
try {
    $db = getDB();
    
    $settings_query = "SELECT es.*, 
                      created_admin.first_name as created_by_first_name, created_admin.last_name as created_by_last_name,
                      updated_admin.first_name as updated_by_first_name, updated_admin.last_name as updated_by_last_name
                      FROM email_settings es
                      LEFT JOIN accounts created_admin ON es.created_by = created_admin.id
                      LEFT JOIN accounts updated_admin ON es.updated_by = updated_admin.id
                      WHERE es.is_active = 1 
                      ORDER BY es.created_at DESC 
                      LIMIT 1";
    $settings_result = $db->query($settings_query);
    $current_settings = $settings_result ? $settings_result->fetch_assoc() : null;
    
    // Get recent email logs
    $logs_query = "SELECT el.*, a.first_name, a.last_name 
                   FROM email_logs el
                   LEFT JOIN accounts a ON el.sent_by = a.id
                   ORDER BY el.sent_at DESC 
                   LIMIT 10";
    $logs_result = $db->query($logs_query);
    $recent_logs = [];
    if ($logs_result) {
        while ($row = $logs_result->fetch_assoc()) {
            $recent_logs[] = $row;
        }
    }
    
} catch (Exception $e) {
    $error = "Failed to load email settings: " . $e->getMessage();
    $current_settings = null;
    $recent_logs = [];
}

// Function to send test email
function sendTestEmail($recipient, $settings, $method) {
    try {
        use PHPMailer\PHPMailer\PHPMailer;
        use PHPMailer\PHPMailer\Exception;
        
        $mail = new PHPMailer(true);
        
        if ($method === 'smtp') {
            $mail->isSMTP();
            $mail->Host = $settings['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $settings['smtp_username'];
            $mail->Password = base64_decode($settings['smtp_password']); // Decrypt password
            $mail->SMTPSecure = $settings['smtp_encryption'];
            $mail->Port = $settings['smtp_port'];
        }
        
        $mail->setFrom($settings['sender_email'], $settings['sender_name']);
        $mail->addAddress($recipient);
        $mail->isHTML(true);
        $mail->Subject = 'Email System Test - ' . date('Y-m-d H:i:s');
        
        $mail->Body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
            <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; margin: 0;">🏦 Online Banking System</h1>
                    <p style="color: #7f8c8d; margin: 10px 0 0 0;">Email System Test</p>
                </div>
                
                <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
                    <h2 style="color: #155724; margin: 0 0 15px 0;">✅ Email System Working!</h2>
                    <p style="color: #155724; margin: 0; line-height: 1.6;">
                        This is a test email to verify that your email configuration is working correctly.
                    </p>
                </div>
                
                <div style="margin: 30px 0;">
                    <h3 style="color: #2c3e50; margin: 0 0 15px 0;">Test Details:</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; width: 30%;">Method:</td>
                            <td style="padding: 8px 0; color: #2c3e50; font-weight: bold;">' . strtoupper($method) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d;">Sent At:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . date('Y-m-d H:i:s') . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d;">Recipient:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . $recipient . '</td>
                        </tr>
                    </table>
                </div>
                
                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                    <p style="color: #7f8c8d; margin: 0; font-size: 14px;">
                        This email was sent automatically by the Online Banking System.<br>
                        If you received this email unexpectedly, please contact your system administrator.
                    </p>
                </div>
            </div>
        </div>';
        
        $result = $mail->send();
        return ['success' => true, 'error' => ''];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Email Settings</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo $success; ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <!-- Current Settings Display -->
    <div class="col-md-8">
        <?php if ($current_settings): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-envelope me-2"></i>
                    Current Email Configuration
                </h3>
                <div class="card-actions">
                    <button type="button" class="btn btn-primary btn-sm" onclick="editEmailSettings()">
                        <i class="fas fa-edit me-2"></i>
                        Edit Settings
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Mail Method:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-<?php echo $current_settings['mail_method'] === 'smtp' ? 'success' : 'info'; ?> badge-sm">
                                    <?php echo strtoupper($current_settings['mail_method']); ?>
                                </span>
                            </dd>

                            <dt class="col-sm-5">SMTP Host:</dt>
                            <dd class="col-sm-7"><code><?php echo htmlspecialchars($current_settings['smtp_host']); ?></code></dd>

                            <dt class="col-sm-5">SMTP Port:</dt>
                            <dd class="col-sm-7"><code><?php echo $current_settings['smtp_port']; ?></code></dd>

                            <dt class="col-sm-5">Encryption:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-secondary badge-sm"><?php echo strtoupper($current_settings['smtp_encryption']); ?></span>
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Sender Name:</dt>
                            <dd class="col-sm-7"><strong><?php echo htmlspecialchars($current_settings['sender_name']); ?></strong></dd>

                            <dt class="col-sm-5">Sender Email:</dt>
                            <dd class="col-sm-7"><?php echo htmlspecialchars($current_settings['sender_email']); ?></dd>

                            <dt class="col-sm-5">Username:</dt>
                            <dd class="col-sm-7"><?php echo htmlspecialchars($current_settings['smtp_username']); ?></dd>

                            <dt class="col-sm-5">Password:</dt>
                            <dd class="col-sm-7">
                                <span class="text-muted">••••••••••••</span>
                                <small class="text-success">(Configured)</small>
                            </dd>
                        </dl>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>Last Updated:</strong> <?php echo date('M j, Y g:i A', strtotime($current_settings['updated_at'])); ?>
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>Updated By:</strong> <?php echo htmlspecialchars($current_settings['updated_by_first_name'] . ' ' . $current_settings['updated_by_last_name']); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Email Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-paper-plane me-2"></i>
                    Test Email Configuration
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="test_email">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Test Email Address <span class="text-danger">*</span></label>
                                <input type="email" name="test_email" class="form-control"
                                       placeholder="<EMAIL>"
                                       value="<?php echo htmlspecialchars($_SESSION['email'] ?? ''); ?>" required>
                                <small class="form-hint">Email address to send test message to</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Test Method</label>
                                <select name="test_method" class="form-select">
                                    <option value="smtp">SMTP (Recommended)</option>
                                    <option value="local">Local Mail Function</option>
                                </select>
                                <small class="form-hint">Choose which method to test</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-footer">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-paper-plane me-2"></i>
                            Send Test Email
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <?php else: ?>
        <div class="card mb-4">
            <div class="card-body">
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-envelope" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No Email Settings</p>
                    <p class="empty-subtitle text-muted">
                        No email configuration has been set up yet.
                    </p>
                    <div class="empty-action">
                        <button type="button" class="btn btn-primary" onclick="editEmailSettings()">
                            <i class="fas fa-plus me-2"></i>
                            Configure Email Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Edit Form (Hidden by default) -->
        <div class="card" id="editForm" style="display: none;">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-edit me-2"></i>
                    Update Email Settings
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="update_settings">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Mail Method <span class="text-danger">*</span></label>
                                <select name="mail_method" class="form-select" required>
                                    <option value="smtp" <?php echo ($current_settings['mail_method'] ?? 'smtp') === 'smtp' ? 'selected' : ''; ?>>
                                        SMTP (Recommended for Production)
                                    </option>
                                    <option value="local" <?php echo ($current_settings['mail_method'] ?? '') === 'local' ? 'selected' : ''; ?>>
                                        Local Mail Function (For Testing)
                                    </option>
                                </select>
                                <small class="form-hint">Choose email sending method</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">SMTP Host <span class="text-danger">*</span></label>
                                <input type="text" name="smtp_host" class="form-control"
                                       placeholder="smtp.hostinger.com"
                                       value="<?php echo htmlspecialchars($current_settings['smtp_host'] ?? 'smtp.hostinger.com'); ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">SMTP Port <span class="text-danger">*</span></label>
                                <input type="number" name="smtp_port" class="form-control"
                                       placeholder="587" min="1" max="65535"
                                       value="<?php echo $current_settings['smtp_port'] ?? 587; ?>" required>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Encryption</label>
                                <select name="smtp_encryption" class="form-select">
                                    <option value="tls" <?php echo ($current_settings['smtp_encryption'] ?? 'tls') === 'tls' ? 'selected' : ''; ?>>TLS (Recommended)</option>
                                    <option value="ssl" <?php echo ($current_settings['smtp_encryption'] ?? '') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                    <option value="none" <?php echo ($current_settings['smtp_encryption'] ?? '') === 'none' ? 'selected' : ''; ?>>None</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">SMTP Username <span class="text-danger">*</span></label>
                                <input type="text" name="smtp_username" class="form-control"
                                       placeholder="<EMAIL>"
                                       value="<?php echo htmlspecialchars($current_settings['smtp_username'] ?? '<EMAIL>'); ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">SMTP Password</label>
                                <input type="password" name="smtp_password" class="form-control"
                                       placeholder="Leave empty to keep current password">
                                <small class="form-hint">Leave empty to keep current password</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Sender Name <span class="text-danger">*</span></label>
                                <input type="text" name="sender_name" class="form-control"
                                       placeholder="Online Banking System"
                                       value="<?php echo htmlspecialchars($current_settings['sender_name'] ?? 'Online Banking System'); ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Sender Email <span class="text-danger">*</span></label>
                                <input type="email" name="sender_email" class="form-control"
                                       placeholder="<EMAIL>"
                                       value="<?php echo htmlspecialchars($current_settings['sender_email'] ?? '<EMAIL>'); ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Save Email Settings
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="cancelEdit()">
                            <i class="fas fa-times me-2"></i>
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Guidelines and Recent Logs -->
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Email Configuration Guide
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h4 class="alert-title">SMTP vs Local</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li><strong>SMTP:</strong> Reliable delivery, professional emails</li>
                        <li><strong>Local:</strong> Quick testing, may end up in spam</li>
                        <li>Use SMTP for production environments</li>
                        <li>Test both methods to ensure functionality</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h4 class="alert-title">Security Notes</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Passwords are encrypted in database</li>
                        <li>Use TLS encryption for secure transmission</li>
                        <li>Test email functionality regularly</li>
                        <li>Monitor email logs for issues</li>
                    </ul>
                </div>

                <div class="alert alert-success">
                    <h4 class="alert-title">Current Configuration</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li><strong>Host:</strong> smtp.hostinger.com</li>
                        <li><strong>Port:</strong> 587 (TLS)</li>
                        <li><strong>Username:</strong> <EMAIL></li>
                        <li><strong>Purpose:</strong> OTP, notifications, alerts</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Recent Email Logs -->
        <?php if (!empty($recent_logs)): ?>
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history me-2"></i>
                    Recent Email Activity
                </h3>
                <div class="card-actions">
                    <a href="email-logs.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-list me-2"></i>
                        View All Logs
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <?php foreach (array_slice($recent_logs, 0, 5) as $log): ?>
                    <div class="list-group-item">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="status-dot status-dot-animated bg-<?php echo $log['status'] === 'sent' ? 'green' : 'red'; ?>"></span>
                            </div>
                            <div class="col text-truncate">
                                <div class="text-reset d-block"><?php echo htmlspecialchars($log['recipient_email']); ?></div>
                                <div class="d-block text-muted text-truncate mt-n1">
                                    <?php echo htmlspecialchars($log['subject']); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-<?php echo $log['status'] === 'sent' ? 'success' : 'danger'; ?> badge-sm">
                                    <?php echo ucfirst($log['status']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
function editEmailSettings() {
    document.getElementById('editForm').style.display = 'block';
    document.getElementById('editForm').scrollIntoView({ behavior: 'smooth' });
}

function cancelEdit() {
    document.getElementById('editForm').style.display = 'none';
}
</script>

<?php include 'includes/admin-footer.php'; ?>
