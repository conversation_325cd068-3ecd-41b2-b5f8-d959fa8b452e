<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../auth/login.php');
}

$page_title = 'Credit/Debit Users';

// Define page actions
$page_actions = [
    [
        'url' => 'users.php',
        'label' => 'View All Users',
        'icon' => 'fas fa-users'
    ]
];

// Error handling and debugging
$errors = [];
$success = '';

// Handle credit/debit operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = intval($_POST['user_id'] ?? 0);
    $amount = floatval($_POST['amount'] ?? 0);
    $description = sanitizeInput($_POST['description'] ?? '');

    if ($user_id <= 0) {
        $errors[] = 'Please select a valid user.';
    }

    if ($amount <= 0) {
        $errors[] = 'Amount must be greater than zero.';
    }

    if (empty($description)) {
        $errors[] = 'Description is required.';
    }

    if (empty($errors)) {
        try {
            $db = getDB();
            $db->beginTransaction();

            // Get user info
            $user_result = $db->query("SELECT * FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);

            if ($user_result->num_rows === 0) {
                $errors[] = 'User not found.';
            } else {
                $user = $user_result->fetch_assoc();
                $old_balance = $user['balance'];

                if ($action === 'credit') {
                    $new_balance = $old_balance + $amount;
                    $operation = 'Credit';
                } else {
                    $new_balance = $old_balance - $amount;
                    $operation = 'Debit';

                    if ($new_balance < 0) {
                        $errors[] = 'Insufficient balance for debit operation.';
                    }
                }

                if (empty($errors)) {
                    // Update user balance
                    $db->query("UPDATE accounts SET balance = ? WHERE id = ?", [$new_balance, $user_id]);

                    // Log the transaction
                    $db->query("INSERT INTO transfers (sender_id, recipient_id, amount, description, status, type, created_at) VALUES (?, ?, ?, ?, 'completed', ?, NOW())",
                        [$action === 'credit' ? 0 : $user_id, $action === 'credit' ? $user_id : 0, $amount, $description, $action]);

                    $db->commit();
                    $success = "$operation of " . formatCurrency($amount) . " successful! New balance: " . formatCurrency($new_balance);
                }
            }

        } catch (Exception $e) {
            $db->rollback();
            error_log("Credit/Debit error: " . $e->getMessage());
            $errors[] = 'Operation failed. Please try again.';
        }
    }
}

// Get users for dropdown
try {
    $db = getDB();
    $users_result = $db->query("SELECT id, first_name, last_name, username, balance, currency FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name");
} catch (Exception $e) {
    error_log("Users fetch error: " . $e->getMessage());
    $users_result = null;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Credit/Debit Users</li>
    </ol>
</nav>

<?php if (!empty($errors)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div>
            <i class="fas fa-exclamation-circle me-2"></i>
        </div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (!empty($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div>
            <i class="fas fa-check-circle me-2"></i>
        </div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row row-cards">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-coins me-2"></i>
                    Credit/Debit User Account
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="mb-3">
                        <label class="form-label required">Select User</label>
                        <select name="user_id" class="form-select" required>
                            <option value="">Choose a user...</option>
                            <?php if ($users_result): ?>
                            <?php while ($user = $users_result->fetch_assoc()): ?>
                            <option value="<?php echo $user['id']; ?>">
                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                (@<?php echo htmlspecialchars($user['username']); ?>) -
                                Balance: <?php echo formatCurrency($user['balance']); ?>
                            </option>
                            <?php endwhile; ?>
                            <?php endif; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label required">Operation Type</label>
                        <div class="form-selectgroup">
                            <label class="form-selectgroup-item">
                                <input type="radio" name="action" value="credit" class="form-selectgroup-input" checked>
                                <span class="form-selectgroup-label">
                                    <i class="fas fa-plus-circle text-success me-2"></i>
                                    Credit (Add Funds)
                                </span>
                            </label>
                            <label class="form-selectgroup-item">
                                <input type="radio" name="action" value="debit" class="form-selectgroup-input">
                                <span class="form-selectgroup-label">
                                    <i class="fas fa-minus-circle text-danger me-2"></i>
                                    Debit (Deduct Funds)
                                </span>
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label required">Amount</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" name="amount" class="form-control" step="0.01" min="0.01" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label required">Description</label>
                        <textarea name="description" class="form-control" rows="3" placeholder="Enter reason for this operation..." required></textarea>
                    </div>

                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check me-2"></i>
                            Process Operation
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Operation Guidelines
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h4 class="alert-title">Credit Operations</h4>
                    <p class="mb-0">Use credit operations to add funds to user accounts for deposits, bonuses, or balance corrections.</p>
                </div>

                <div class="alert alert-warning">
                    <h4 class="alert-title">Debit Operations</h4>
                    <p class="mb-0">Use debit operations to deduct funds for withdrawals, fees, or balance corrections. Ensure sufficient balance before debiting.</p>
                </div>

                <div class="alert alert-secondary">
                    <h4 class="alert-title">Best Practices</h4>
                    <ul class="mb-0">
                        <li>Always provide clear descriptions</li>
                        <li>Verify user details before processing</li>
                        <li>Check current balance for debit operations</li>
                        <li>All operations are logged for audit purposes</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
