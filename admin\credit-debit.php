<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Credit/Debit Users';

// Define page actions
$page_actions = [
    [
        'url' => 'users.php',
        'label' => 'View All Users',
        'icon' => 'fas fa-users'
    ]
];

// Error handling and debugging
$errors = [];
$success = '';

// Handle credit/debit operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = sanitizeInput($_POST['action'] ?? '');
    $user_id = intval($_POST['user_id'] ?? 0);
    $amount = floatval($_POST['amount'] ?? 0);
    $description = sanitizeInput($_POST['description'] ?? '');
    $transaction_date = sanitizeInput($_POST['transaction_date'] ?? date('Y-m-d'));
    
    // Enhanced logging for debugging
    error_log("=== CREDIT/DEBIT OPERATION START ===");
    error_log("Action: $action, User ID: $user_id, Amount: $amount, Date: $transaction_date");
    error_log("Admin ID: " . $_SESSION['user_id']);
    
    // Validation
    if ($user_id <= 0) {
        $errors[] = 'Please select a valid user.';
        error_log("ERROR: Invalid user ID - $user_id");
    }
    
    if ($amount <= 0) {
        $errors[] = 'Amount must be greater than zero.';
        error_log("ERROR: Invalid amount - $amount");
    }
    
    if (empty($description)) {
        $errors[] = 'Description is required.';
        error_log("ERROR: Empty description");
    }
    
    if (!in_array($action, ['credit', 'debit'])) {
        $errors[] = 'Invalid operation type.';
        error_log("ERROR: Invalid action - $action");
    }
    
    // Validate date
    if (!DateTime::createFromFormat('Y-m-d', $transaction_date)) {
        $errors[] = 'Invalid transaction date.';
        error_log("ERROR: Invalid date format - $transaction_date");
    }
    
    if (empty($errors)) {
        try {
            $db = getDB();
            $db->beginTransaction();
            
            error_log("Starting database transaction...");
            
            // Get user info with proper error handling
            $user_result = $db->query("SELECT id, first_name, last_name, username, account_number, balance, currency FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);
            
            if (!$user_result || $user_result->num_rows === 0) {
                throw new Exception("User not found with ID: $user_id");
            }
            
            $user = $user_result->fetch_assoc();
            $old_balance = floatval($user['balance']);
            
            error_log("User found: {$user['username']} (ID: {$user['id']}) - Current balance: $old_balance");
            
            if ($action === 'credit') {
                $new_balance = $old_balance + $amount;
                $operation = 'Credit';
                $transaction_type = 'credit';
            } else {
                $new_balance = $old_balance - $amount;
                $operation = 'Debit';
                $transaction_type = 'debit';
                
                if ($new_balance < 0) {
                    throw new Exception("Insufficient balance for debit operation. Current balance: " . formatCurrency($old_balance, $user['currency']));
                }
            }
            
            error_log("Calculated new balance: $new_balance");
            
            // Update user balance
            $update_result = $db->query("UPDATE accounts SET balance = ? WHERE id = ?", [$new_balance, $user_id]);
            if (!$update_result) {
                throw new Exception("Failed to update user balance");
            }
            
            error_log("Balance updated successfully");
            
            // Generate reference number
            $reference = 'ADM' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // Record in account_transactions table
            $transaction_sql = "INSERT INTO account_transactions (
                account_id, transaction_type, amount, currency, description, 
                reference_number, category, status, processed_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, 'adjustment', 'completed', ?, ?)";
            
            $transaction_result = $db->query($transaction_sql, [
                $user_id, 
                $transaction_type, 
                $amount, 
                $user['currency'] ?? 'USD', 
                $description,
                $reference, 
                $_SESSION['user_id'],
                $transaction_date . ' ' . date('H:i:s')
            ]);
            
            if (!$transaction_result) {
                throw new Exception("Failed to record transaction");
            }
            
            error_log("Transaction recorded with reference: $reference");
            
            // Log activity for audit
            logActivity($_SESSION['user_id'], "Admin {$action}ed account", 'accounts', $user_id, 
                       ['balance' => $old_balance], ['balance' => $new_balance]);
            
            $db->commit();
            
            $success = "$operation of " . formatCurrency($amount, $user['currency']) . " successful! New balance: " . formatCurrency($new_balance, $user['currency']) . " (Ref: $reference)";
            error_log("=== OPERATION COMPLETED SUCCESSFULLY ===");
            
        } catch (Exception $e) {
            if (isset($db)) {
                $db->rollback();
            }
            $error_msg = "Credit/Debit operation failed: " . $e->getMessage();
            error_log("=== OPERATION FAILED ===");
            error_log($error_msg);
            $errors[] = $e->getMessage();
        }
    } else {
        error_log("=== VALIDATION ERRORS ===");
        foreach ($errors as $error) {
            error_log("Validation Error: $error");
        }
    }
}

// Get users for dropdown
try {
    $db = getDB();
    $users_result = $db->query("SELECT id, first_name, last_name, username, account_number, balance, currency FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name");
    error_log("Users loaded successfully - Count: " . ($users_result ? $users_result->num_rows : 0));
} catch (Exception $e) {
    error_log("Users fetch error: " . $e->getMessage());
    $users_result = null;
    $errors[] = 'Failed to load users: ' . $e->getMessage();
}

// Get recent transactions for editing
try {
    $recent_transactions = $db->query("
        SELECT at.*, a.first_name, a.last_name, a.username, a.account_number,
               admin.first_name as admin_first_name, admin.last_name as admin_last_name
        FROM account_transactions at
        LEFT JOIN accounts a ON at.account_id = a.id
        LEFT JOIN accounts admin ON at.processed_by = admin.id
        WHERE at.category = 'adjustment'
        ORDER BY at.created_at DESC
        LIMIT 10
    ");
} catch (Exception $e) {
    error_log("Recent transactions fetch error: " . $e->getMessage());
    $recent_transactions = null;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Credit/Debit Users</li>
    </ol>
</nav>

<?php if (!empty($errors)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div>
            <i class="fas fa-exclamation-circle me-2"></i>
        </div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
</div>
<script>
console.error('Credit/Debit Errors:', <?php echo json_encode($errors); ?>);
</script>
<?php endif; ?>

<?php if (!empty($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div>
            <i class="fas fa-check-circle me-2"></i>
        </div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<script>
console.log('Credit/Debit Success:', <?php echo json_encode($success); ?>);
</script>
<?php endif; ?>

<div class="row row-cards">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-coins me-2"></i>
                    Credit/Debit User Account
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="mb-3">
                        <label class="form-label required">Select User</label>
                        <select name="user_id" class="form-select" required>
                            <option value="">Choose a user...</option>
                            <?php if ($users_result && $users_result->num_rows > 0): ?>
                            <?php while ($user = $users_result->fetch_assoc()): ?>
                            <option value="<?php echo $user['id']; ?>">
                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                (@<?php echo htmlspecialchars($user['username']); ?>) -
                                Balance: <?php echo formatCurrency($user['balance'], $user['currency'] ?? 'USD'); ?>
                            </option>
                            <?php endwhile; ?>
                            <?php else: ?>
                            <option value="" disabled>No users found</option>
                            <?php endif; ?>
                        </select>
                        <div class="form-hint">Select the user account to credit or debit</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label required">Operation Type</label>
                        <div class="form-selectgroup">
                            <label class="form-selectgroup-item">
                                <input type="radio" name="action" value="credit" class="form-selectgroup-input" checked>
                                <span class="form-selectgroup-label">
                                    <i class="fas fa-plus-circle text-success me-2"></i>
                                    Credit (Add Funds)
                                </span>
                            </label>
                            <label class="form-selectgroup-item">
                                <input type="radio" name="action" value="debit" class="form-selectgroup-input">
                                <span class="form-selectgroup-label">
                                    <i class="fas fa-minus-circle text-danger me-2"></i>
                                    Debit (Deduct Funds)
                                </span>
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label required">Amount</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" name="amount" class="form-control" step="0.01" min="0.01" required>
                        </div>
                        <div class="form-hint">Enter the amount to credit or debit</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label required">Transaction Date</label>
                        <input type="date" name="transaction_date" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
                        <div class="form-hint">Select current date or backdate the transaction</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label required">Description</label>
                        <textarea name="description" class="form-control" rows="3" placeholder="Enter reason for this operation..." required></textarea>
                        <div class="form-hint">Provide a clear description for audit purposes</div>
                    </div>

                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check me-2"></i>
                            Process Operation
                        </button>
                        <button type="reset" class="btn btn-secondary ms-2">
                            <i class="fas fa-undo me-2"></i>
                            Reset Form
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Operation Guidelines
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-secondary">
                    <h4 class="alert-title">Best Practices</h4>
                    <ul class="mb-0">
                        <li>Always provide clear descriptions</li>
                        <li>Verify user details before processing</li>
                        <li>Check current balance for debit operations</li>
                        <li>Use appropriate transaction dates</li>
                        <li>All operations are logged for audit purposes</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<?php if ($recent_transactions && $recent_transactions->num_rows > 0): ?>
<div class="row row-cards mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history me-2"></i>
                    Recent Credit/Debit Transactions
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>User</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Reference</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($transaction = $recent_transactions->fetch_assoc()): ?>
                            <tr>
                                <td>
                                    <div class="text-muted"><?php echo formatDate($transaction['created_at']); ?></div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2">
                                            <?php echo strtoupper(substr($transaction['first_name'], 0, 1) . substr($transaction['last_name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($transaction['first_name'] . ' ' . $transaction['last_name']); ?></div>
                                            <div class="text-muted">@<?php echo htmlspecialchars($transaction['username']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($transaction['transaction_type'] === 'credit'): ?>
                                    <span class="badge bg-success">Credit</span>
                                    <?php else: ?>
                                    <span class="badge bg-danger">Debit</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="fw-bold <?php echo $transaction['transaction_type'] === 'credit' ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo ($transaction['transaction_type'] === 'credit' ? '+' : '-') . formatCurrency($transaction['amount'], $transaction['currency']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($transaction['description']); ?>">
                                        <?php echo htmlspecialchars($transaction['description']); ?>
                                    </div>
                                </td>
                                <td>
                                    <select class="form-select form-select-sm" onchange="updateTransactionStatus(<?php echo $transaction['id']; ?>, this.value)">
                                        <option value="pending" <?php echo $transaction['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="processing" <?php echo $transaction['status'] === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                        <option value="completed" <?php echo $transaction['status'] === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                        <option value="cancelled" <?php echo $transaction['status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                    </select>
                                </td>
                                <td>
                                    <small class="text-muted"><?php echo htmlspecialchars($transaction['reference_number']); ?></small>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Transaction status update function
function updateTransactionStatus(transactionId, newStatus) {
    console.log('Updating transaction status:', transactionId, newStatus);

    fetch('update-transaction-status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            transaction_id: transactionId,
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('Status update response:', data);
        if (data.success) {
            showNotification('Transaction status updated successfully', 'success');
        } else {
            showNotification('Failed to update transaction status: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error updating transaction status:', error);
        showNotification('Error updating transaction status', 'error');
    });
}

// Notification system
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' :
                     type === 'error' ? 'alert-danger' :
                     type === 'info' ? 'alert-info' : 'alert-warning';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);

    console.log('Notification shown:', type, message);
}
</script>

<?php include 'includes/admin-footer.php'; ?>
