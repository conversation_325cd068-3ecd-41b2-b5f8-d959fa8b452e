<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('auth/login.php');
}

$page_title = 'Credit/Debit Users';

// Define page actions
$page_actions = [
    [
        'url' => 'users.php',
        'label' => 'View All Users',
        'icon' => 'fas fa-users'
    ]
];

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Credit/Debit Users</li>
    </ol>
</nav>

<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-coins me-2"></i>
                    Credit/Debit User Accounts
                </h3>
            </div>
            <div class="card-body">
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-coins" style="font-size: 4rem; color: #28a745;"></i>
                    </div>
                    <p class="empty-title">Credit/Debit Management</p>
                    <p class="empty-subtitle text-muted">
                        This page will contain user account credit/debit functionality including:
                    </p>
                    <ul class="text-start" style="max-width: 450px; margin: 0 auto;">
                        <li><strong>Credit User Accounts:</strong> Add funds to user balances</li>
                        <li><strong>Debit User Accounts:</strong> Deduct funds from user balances</li>
                        <li><strong>Balance Adjustments:</strong> Manual balance corrections</li>
                        <li><strong>Transaction History:</strong> View all credit/debit operations</li>
                        <li><strong>Bulk Operations:</strong> Credit/debit multiple users</li>
                        <li><strong>Currency Settings:</strong> Multi-currency support</li>
                        <li><strong>Approval Workflow:</strong> Require approval for large amounts</li>
                    </ul>
                    <div class="empty-action mt-4">
                        <a href="users.php" class="btn btn-primary me-2">
                            <i class="fas fa-users me-2"></i>
                            View Users
                        </a>
                        <a href="transactions.php" class="btn btn-outline-primary">
                            <i class="fas fa-exchange-alt me-2"></i>
                            View Transactions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
