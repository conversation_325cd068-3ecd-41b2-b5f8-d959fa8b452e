<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'User Account Status Management';

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_status') {
    try {
        $db = getDB();
        
        $account_id = intval($_POST['account_id']);
        $new_status = trim($_POST['new_status']);
        $reason = trim($_POST['reason']);
        
        // Validate inputs
        if (empty($account_id) || empty($new_status) || empty($reason)) {
            throw new Exception("All fields are required.");
        }
        
        // Get current user status
        $current_user_query = "SELECT account_status, first_name, last_name FROM accounts WHERE id = ?";
        $current_user_result = $db->query($current_user_query, [$account_id]);
        $current_user = $current_user_result->fetch_assoc();
        
        if (!$current_user) {
            throw new Exception("User not found.");
        }
        
        $old_status = $current_user['account_status'];
        
        // Update user status
        $update_status = "UPDATE accounts SET account_status = ? WHERE id = ?";
        $update_result = $db->query($update_status, [$new_status, $account_id]);

        if (!$update_result) {
            throw new Exception("Failed to update user status in database.");
        }

        // Log status change
        $log_change = "INSERT INTO user_status_history (account_id, old_status, new_status, reason, changed_by)
                      VALUES (?, ?, ?, ?, ?)";
        $log_result = $db->query($log_change, [$account_id, $old_status, $new_status, $reason, $_SESSION['user_id']]);

        if (!$log_result) {
            throw new Exception("Failed to log status change.");
        }

        $success = "✅ Account status updated successfully for " . $current_user['first_name'] . " " . $current_user['last_name'] .
                  " from '" . ($old_status ?: 'Active') . "' to '" . $new_status . "'";
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_status = $_GET['status'] ?? '';
$filter_user = $_GET['user'] ?? '';

// Build WHERE clause for filters
$where_conditions = ["a.is_admin = 0"]; // Only non-admin users
$params = [];

if (!empty($filter_status)) {
    $where_conditions[] = "a.account_status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_user)) {
    $where_conditions[] = "(a.first_name LIKE ? OR a.last_name LIKE ? OR a.username LIKE ?)";
    $search_term = "%$filter_user%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = "WHERE " . implode(" AND ", $where_conditions);

try {
    $db = getDB();

    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total FROM accounts a $where_clause";
    if (!empty($params)) {
        $count_result = $db->query($count_query, $params);
    } else {
        $count_result = $db->query($count_query);
    }
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);

    // Get users with pagination - simplified query first
    $users_query = "SELECT a.id, a.first_name, a.last_name, a.username, a.email, a.phone,
                    a.date_of_birth, a.address, a.account_number, a.balance, a.created_at, a.last_login,
                    COALESCE(a.account_status, 'Active') as account_status
                    FROM accounts a
                    $where_clause
                    ORDER BY a.created_at DESC
                    LIMIT $records_per_page OFFSET $offset";

    if (!empty($params)) {
        $users_result = $db->query($users_query, $params);
    } else {
        $users_result = $db->query($users_query);
    }

    $users = [];
    if ($users_result) {
        while ($row = $users_result->fetch_assoc()) {
            // Get last status change for this user
            $status_history_query = "SELECT ush.changed_at, admin.first_name as admin_first_name, admin.last_name as admin_last_name
                                    FROM user_status_history ush
                                    LEFT JOIN accounts admin ON ush.changed_by = admin.id
                                    WHERE ush.account_id = ?
                                    ORDER BY ush.changed_at DESC
                                    LIMIT 1";
            $status_result = $db->query($status_history_query, [$row['id']]);
            $status_info = $status_result ? $status_result->fetch_assoc() : null;

            if ($status_info) {
                $row['last_status_change'] = $status_info['changed_at'];
                $row['admin_first_name'] = $status_info['admin_first_name'];
                $row['admin_last_name'] = $status_info['admin_last_name'];
            } else {
                $row['last_status_change'] = null;
                $row['admin_first_name'] = null;
                $row['admin_last_name'] = null;
            }

            $users[] = $row;
        }
    }

    // Get status statistics
    $stats_query = "SELECT
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN COALESCE(account_status, 'Active') = 'Active' THEN 1 END) as active_users,
                    COUNT(CASE WHEN account_status = 'Dormant/Inactive' THEN 1 END) as dormant_users,
                    COUNT(CASE WHEN account_status = 'INACTIVE' THEN 1 END) as inactive_users,
                    COUNT(CASE WHEN account_status IN ('Disabled', 'Suspend') THEN 1 END) as disabled_users
                    FROM accounts WHERE is_admin = 0";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result ? $stats_result->fetch_assoc() : ['total_users' => 0, 'active_users' => 0, 'dormant_users' => 0, 'inactive_users' => 0, 'disabled_users' => 0];

    // Get unique status values for filter
    $status_query = "SELECT DISTINCT COALESCE(account_status, 'Active') as account_status FROM accounts WHERE is_admin = 0 ORDER BY account_status";
    $status_result = $db->query($status_query);
    $status_options = [];
    if ($status_result) {
        while ($status = $status_result->fetch_assoc()) {
            $status_options[] = $status['account_status'];
        }
    }

} catch (Exception $e) {
    $error = "Failed to load users: " . $e->getMessage();
    $users = [];
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total_users' => 0, 'active_users' => 0, 'dormant_users' => 0, 'inactive_users' => 0, 'disabled_users' => 0];
    $status_options = [];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">User Status Management</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo $success; ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-users"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total_users']); ?></div>
                        <div class="text-muted">Total Users</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check-circle"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['active_users']); ?></div>
                        <div class="text-muted">Active</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-pause-circle"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['dormant_users'] + $stats['inactive_users']); ?></div>
                        <div class="text-muted">Inactive</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-danger text-white avatar">
                            <i class="fas fa-ban"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['disabled_users']); ?></div>
                        <div class="text-muted">Disabled/Suspended</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    User Filters
                </h3>
                <div class="card-actions">
                    <a href="user-status-management.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <div class="col-md-3">
                        <label class="form-label">Account Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <?php foreach ($status_options as $status): ?>
                            <option value="<?php echo htmlspecialchars($status); ?>" <?php echo $filter_status === $status ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($status); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">User</label>
                        <input type="text" name="user" class="form-control form-control-sm" placeholder="Search user..." value="<?php echo htmlspecialchars($filter_user); ?>">
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">&nbsp;</label>
                        <div class="btn-group d-block" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="user-status-management.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-users me-2"></i>
                    User Account Status Management
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> users</span>
                </h3>
                <div class="card-actions">
                    <span class="text-muted">
                        Showing <?php echo number_format(($current_page - 1) * $records_per_page + 1); ?> -
                        <?php echo number_format(min($current_page * $records_per_page, $total_records)); ?>
                        of <?php echo number_format($total_records); ?> users
                    </span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($users)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>User Details</th>
                                <th>Account Number</th>
                                <th>Current Status</th>
                                <th>Last Status Change</th>
                                <th>Changed By</th>
                                <th>Joined Date</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($users as $user):
                            ?>
                            <tr>
                                <!-- Row Number -->
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <!-- User Details -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-xs me-2">
                                            <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold" style="font-size: 0.85rem;"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                            <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                        </div>
                                    </div>
                                </td>

                                <!-- Account Number -->
                                <td>
                                    <code><?php echo htmlspecialchars($user['account_number']); ?></code>
                                </td>

                                <!-- Current Status -->
                                <td>
                                    <?php
                                    $status_colors = [
                                        'Active' => 'success',
                                        'Dormant/Inactive' => 'warning',
                                        'INACTIVE' => 'secondary',
                                        'Disabled' => 'danger',
                                        'Suspend' => 'danger'
                                    ];
                                    $status_color = $status_colors[$user['account_status']] ?? 'info';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                        <?php echo htmlspecialchars($user['account_status']); ?>
                                    </span>
                                </td>

                                <!-- Last Status Change -->
                                <td>
                                    <?php if ($user['last_status_change']): ?>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($user['last_status_change'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($user['last_status_change'])); ?></small>
                                    </div>
                                    <?php else: ?>
                                    <span class="text-muted">No changes</span>
                                    <?php endif; ?>
                                </td>

                                <!-- Changed By -->
                                <td>
                                    <?php if ($user['admin_first_name']): ?>
                                    <small class="text-muted"><?php echo htmlspecialchars($user['admin_first_name'] . ' ' . $user['admin_last_name']); ?></small>
                                    <?php else: ?>
                                    <span class="text-muted">System</span>
                                    <?php endif; ?>
                                </td>

                                <!-- Joined Date -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($user['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($user['created_at'])); ?></small>
                                    </div>
                                </td>

                                <!-- Actions -->
                                <td>
                                    <div class="btn-list">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewUserDetails('<?php echo $user['id']; ?>')" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="updateUserStatus(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>', '<?php echo htmlspecialchars($user['account_status']); ?>')" title="Update Status">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-users" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No users found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty(array_filter([$filter_status, $filter_user]))): ?>
                        Try adjusting your filters to see more results.
                        <?php else: ?>
                        No users have been registered yet.
                        <?php endif; ?>
                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal modal-blur fade" id="updateStatusModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <form method="POST" action="">
                <div class="modal-header">
                    <h5 class="modal-title">Update Account Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="account_id" id="updateAccountId">

                    <div class="mb-3">
                        <label class="form-label">User</label>
                        <div class="form-control-plaintext" id="updateUserName"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Current Status</label>
                        <div class="form-control-plaintext" id="updateCurrentStatus"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">New Status <span class="text-danger">*</span></label>
                        <select name="new_status" id="newStatusSelect" class="form-select" required>
                            <option value="">Select new status...</option>
                            <option value="Active">Active - User can access all account functions normally</option>
                            <option value="Dormant/Inactive">Dormant/Inactive - User can log in but sees notice and cannot make transfers</option>
                            <option value="INACTIVE">INACTIVE - User cannot log in, receives "Contact Customer Care" message</option>
                            <option value="Disabled">Disabled - User cannot log in due to terms violation</option>
                            <option value="Suspend">Suspend - Same as Disabled status</option>
                        </select>
                        <small class="form-hint">Select the new account status for this user</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Reason for Status Change <span class="text-danger">*</span></label>
                        <textarea name="reason" class="form-control" rows="3" placeholder="Enter detailed reason for this status change..." required></textarea>
                        <small class="form-hint">This reason will be logged for audit purposes</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal modal-blur fade" id="userDetailsModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">User Account Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- User details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function updateUserStatus(accountId, userName, currentStatus) {
    document.getElementById('updateAccountId').value = accountId;
    document.getElementById('updateUserName').textContent = userName;
    document.getElementById('updateCurrentStatus').innerHTML = `<span class="badge bg-info">${currentStatus}</span>`;

    // Reset the form
    document.getElementById('newStatusSelect').value = '';
    document.querySelector('textarea[name="reason"]').value = '';

    // Use jQuery to show modal
    $('#updateStatusModal').modal('show');
}

function viewUserDetails(userId) {
    // Simple alert for now - we can enhance this later
    alert('User Details for ID: ' + userId + '\n\nThis feature will show detailed user information in a future update.');
}

function getStatusColor(status) {
    const colors = {
        'Active': 'success',
        'Dormant/Inactive': 'warning',
        'INACTIVE': 'secondary',
        'Disabled': 'danger',
        'Suspend': 'danger'
    };
    return colors[status] || 'info';
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatTime(dateString) {
    return new Date(dateString).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}
</script>

<?php include 'includes/admin-footer.php'; ?>
