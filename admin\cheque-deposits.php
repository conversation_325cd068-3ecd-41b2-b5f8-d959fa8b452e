<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Cheque Deposit Management';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add_cheque') {
        try {
            $db = getDB();
            
            $account_id = intval($_POST['account_id']);
            $cheque_number = trim($_POST['cheque_number']);
            $deposit_date = $_POST['deposit_date'];
            $sender_name = trim($_POST['sender_name']);
            $amount = floatval($_POST['amount']);
            $currency = $_POST['currency'];
            $account_type = trim($_POST['account_type']);
            $branch_code = trim($_POST['branch_code']);
            $id_passport_number = trim($_POST['id_passport_number']);
            $bank_name = trim($_POST['bank_name']);
            $clearance_status = $_POST['clearance_status'];
            
            // Validate inputs
            if (empty($account_id) || empty($cheque_number) || empty($deposit_date) || 
                empty($sender_name) || $amount <= 0 || empty($currency) || 
                empty($account_type) || empty($branch_code) || empty($id_passport_number) || 
                empty($bank_name)) {
                throw new Exception("All fields are required and amount must be positive.");
            }
            
            // Handle file upload
            if (!isset($_FILES['cheque_image']) || $_FILES['cheque_image']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception("Cheque image is required.");
            }
            
            $upload_dir = '../uploads/cheques/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['cheque_image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
            
            if (!in_array($file_extension, $allowed_extensions)) {
                throw new Exception("Invalid file type. Only JPG, PNG, GIF, and PDF files are allowed.");
            }
            
            $file_name = 'cheque_' . time() . '_' . uniqid() . '.' . $file_extension;
            $file_path = $upload_dir . $file_name;
            
            if (!move_uploaded_file($_FILES['cheque_image']['tmp_name'], $file_path)) {
                throw new Exception("Failed to upload cheque image.");
            }
            
            // Insert cheque deposit record
            $insert_cheque = "INSERT INTO cheque_deposits (
                account_id, cheque_number, deposit_date, sender_name, amount, currency,
                account_type, branch_code, id_passport_number, bank_name, cheque_image_path,
                clearance_status, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $cheque_id = $db->insert($insert_cheque, [
                $account_id, $cheque_number, $deposit_date, $sender_name, $amount, $currency,
                $account_type, $branch_code, $id_passport_number, $bank_name, $file_path,
                $clearance_status, $_SESSION['user_id']
            ]);
            
            if ($cheque_id) {
                $success = "✅ Cheque deposit record added successfully! Cheque ID: {$cheque_id}";
            } else {
                throw new Exception("Failed to add cheque deposit record.");
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    } elseif (isset($_POST['action']) && $_POST['action'] === 'update_status') {
        try {
            $db = getDB();
            
            $cheque_id = intval($_POST['cheque_id']);
            $new_status = $_POST['new_status'];
            $status_reason = trim($_POST['status_reason'] ?? '');
            
            // Update cheque status
            $update_status = "UPDATE cheque_deposits SET 
                             clearance_status = ?, status_reason = ?, updated_by = ? 
                             WHERE id = ?";
            $db->query($update_status, [$new_status, $status_reason, $_SESSION['user_id'], $cheque_id]);
            
            $success = "✅ Cheque clearance status updated successfully!";
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_status = $_GET['status'] ?? '';
$filter_user = $_GET['user'] ?? '';
$filter_date_from = $_GET['date_from'] ?? '';
$filter_date_to = $_GET['date_to'] ?? '';
$filter_amount_min = floatval($_GET['amount_min'] ?? 0);
$filter_amount_max = floatval($_GET['amount_max'] ?? 0);

// Build WHERE clause for filters
$where_conditions = [];
$params = [];

if (!empty($filter_status)) {
    $where_conditions[] = "cd.clearance_status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_user)) {
    $where_conditions[] = "(a.first_name LIKE ? OR a.last_name LIKE ? OR a.username LIKE ?)";
    $search_term = "%$filter_user%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "cd.deposit_date >= ?";
    $params[] = $filter_date_from;
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "cd.deposit_date <= ?";
    $params[] = $filter_date_to;
}

if ($filter_amount_min > 0) {
    $where_conditions[] = "cd.amount >= ?";
    $params[] = $filter_amount_min;
}

if ($filter_amount_max > 0) {
    $where_conditions[] = "cd.amount <= ?";
    $params[] = $filter_amount_max;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total 
                    FROM cheque_deposits cd 
                    LEFT JOIN accounts a ON cd.account_id = a.id 
                    $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get cheque deposits with pagination
    $cheques_query = "SELECT cd.*, 
                      a.first_name, a.last_name, a.username, a.account_number,
                      admin.first_name as admin_first_name, admin.last_name as admin_last_name
                      FROM cheque_deposits cd 
                      LEFT JOIN accounts a ON cd.account_id = a.id 
                      LEFT JOIN accounts admin ON cd.created_by = admin.id 
                      $where_clause
                      ORDER BY cd.created_at DESC 
                      LIMIT $records_per_page OFFSET $offset";
    
    $cheques_result = $db->query($cheques_query, $params);
    $cheques = [];
    while ($row = $cheques_result->fetch_assoc()) {
        $cheques[] = $row;
    }
    
    // Get all users for the dropdown
    $users_query = "SELECT id, first_name, last_name, username, account_number FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name";
    $users_result = $db->query($users_query);
    $users = [];
    while ($row = $users_result->fetch_assoc()) {
        $users[] = $row;
    }
    
    // Get summary statistics
    $stats_query = "SELECT 
                    COUNT(*) as total_cheques,
                    COUNT(CASE WHEN clearance_status = 'pending' THEN 1 END) as pending_cheques,
                    COUNT(CASE WHEN clearance_status = 'processing' THEN 1 END) as processing_cheques,
                    COUNT(CASE WHEN clearance_status = 'cleared' THEN 1 END) as cleared_cheques,
                    COUNT(CASE WHEN clearance_status = 'rejected' THEN 1 END) as rejected_cheques,
                    SUM(CASE WHEN clearance_status = 'cleared' THEN amount ELSE 0 END) as total_cleared_amount
                    FROM cheque_deposits";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result->fetch_assoc();
    
} catch (Exception $e) {
    $error = "Failed to load cheque deposits: " . $e->getMessage();
    $cheques = [];
    $users = [];
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total_cheques' => 0, 'pending_cheques' => 0, 'processing_cheques' => 0, 'cleared_cheques' => 0, 'rejected_cheques' => 0, 'total_cleared_amount' => 0];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Cheque Deposits</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo $success; ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-file-invoice"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total_cheques']); ?></div>
                        <div class="text-muted">Total Cheques</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-clock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['pending_cheques']); ?></div>
                        <div class="text-muted">Pending</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check-circle"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['cleared_cheques']); ?></div>
                        <div class="text-muted">Cleared</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-info text-white avatar">
                            <i class="fas fa-dollar-sign"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">$<?php echo number_format($stats['total_cleared_amount'], 2); ?></div>
                        <div class="text-muted">Cleared Amount</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Cheque Filters
                </h3>
                <div class="card-actions">
                    <a href="cheque-deposits.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                    <button type="button" class="btn btn-success btn-sm" onclick="showAddChequeForm()">
                        <i class="fas fa-plus me-2"></i>
                        Add Cheque Deposit
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="processing" <?php echo $filter_status === 'processing' ? 'selected' : ''; ?>>Processing</option>
                            <option value="cleared" <?php echo $filter_status === 'cleared' ? 'selected' : ''; ?>>Cleared</option>
                            <option value="rejected" <?php echo $filter_status === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">User</label>
                        <input type="text" name="user" class="form-control form-control-sm" placeholder="Search user..." value="<?php echo htmlspecialchars($filter_user); ?>">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Date From</label>
                        <input type="date" name="date_from" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Date To</label>
                        <input type="date" name="date_to" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Amount Range</label>
                        <div class="input-group input-group-sm">
                            <input type="number" name="amount_min" class="form-control" step="0.01" placeholder="Min" value="<?php echo $filter_amount_min > 0 ? $filter_amount_min : ''; ?>">
                            <span class="input-group-text">-</span>
                            <input type="number" name="amount_max" class="form-control" step="0.01" placeholder="Max" value="<?php echo $filter_amount_max > 0 ? $filter_amount_max : ''; ?>">
                        </div>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="btn-group d-block" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="cheque-deposits.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
