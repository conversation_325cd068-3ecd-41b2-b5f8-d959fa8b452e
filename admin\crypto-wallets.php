<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../auth/login.php');
}

$page_title = 'Crypto Wallets';

// Define page actions
$page_actions = [
    [
        'url' => 'btc-integration.php',
        'label' => 'BTC Integration',
        'icon' => 'fab fa-bitcoin'
    ]
];

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Crypto Wallets</li>
    </ol>
</nav>

<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-wallet me-2"></i>
                    Cryptocurrency Wallet Management
                </h3>
            </div>
            <div class="card-body">
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-wallet" style="font-size: 4rem; color: #6f42c1;"></i>
                    </div>
                    <p class="empty-title">Crypto Wallet Management</p>
                    <p class="empty-subtitle text-muted">
                        This page will contain cryptocurrency wallet management functionality including:
                    </p>
                    <ul class="text-start" style="max-width: 500px; margin: 0 auto;">
                        <li><strong>User Crypto Wallets:</strong> View and manage user cryptocurrency wallets</li>
                        <li><strong>Wallet Creation:</strong> Generate new crypto wallets for users</li>
                        <li><strong>Balance Monitoring:</strong> Track cryptocurrency balances across all wallets</li>
                        <li><strong>Address Management:</strong> Generate and manage wallet addresses</li>
                        <li><strong>Multi-Currency Support:</strong> Bitcoin, Ethereum, and other cryptocurrencies</li>
                        <li><strong>Security Controls:</strong> Wallet freezing and security settings</li>
                        <li><strong>Transaction History:</strong> Complete crypto transaction logs</li>
                        <li><strong>Backup & Recovery:</strong> Wallet backup and recovery procedures</li>
                    </ul>
                    <div class="empty-action mt-4">
                        <a href="btc-integration.php" class="btn btn-primary me-2">
                            <i class="fab fa-bitcoin me-2"></i>
                            BTC Integration
                        </a>
                        <a href="users.php" class="btn btn-outline-primary">
                            <i class="fas fa-users me-2"></i>
                            View Users
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
