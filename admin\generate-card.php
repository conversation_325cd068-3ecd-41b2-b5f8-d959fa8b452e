<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Generate Virtual Card';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        $account_id = intval($_POST['account_id']);
        $card_holder_name = trim($_POST['card_holder_name']);
        $daily_limit = floatval($_POST['daily_limit']);
        $monthly_limit = floatval($_POST['monthly_limit']);
        $initial_balance = floatval($_POST['initial_balance'] ?? 0);
        
        // Validate inputs
        if (empty($account_id) || empty($card_holder_name)) {
            throw new Exception("Account and card holder name are required.");
        }
        
        if ($daily_limit <= 0 || $monthly_limit <= 0) {
            throw new Exception("Limits must be greater than zero.");
        }
        
        if ($daily_limit > $monthly_limit) {
            throw new Exception("Daily limit cannot exceed monthly limit.");
        }
        
        // Check if account exists
        $account_check = $db->query("SELECT id, first_name, last_name FROM accounts WHERE id = ? AND is_admin = 0", [$account_id]);
        if (!$account_check->fetch_assoc()) {
            throw new Exception("Invalid account selected.");
        }
        
        // Generate card details
        $card_number = generateCardNumber();
        $cvv = str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT);
        $expiry_date = date('Y-m-d', strtotime('+3 years'));
        
        // Start transaction
        $db->query("START TRANSACTION");
        
        // Insert virtual card
        $insert_card = "INSERT INTO virtual_cards (
            account_id, card_number, card_holder_name, expiry_date, cvv, 
            card_balance, daily_limit, monthly_limit, status, approved_by, approved_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW())";
        
        $card_result = $db->query($insert_card, [
            $account_id, $card_number, $card_holder_name, $expiry_date, $cvv,
            $initial_balance, $daily_limit, $monthly_limit, $_SESSION['user_id']
        ]);
        
        if (!$card_result) {
            throw new Exception("Failed to create virtual card.");
        }
        
        $card_id = $db->lastInsertId();
        
        // If initial balance > 0, create a credit transaction
        if ($initial_balance > 0) {
            $reference = 'CARD' . date('Ymd') . str_pad($card_id, 6, '0', STR_PAD_LEFT);
            
            $insert_transaction = "INSERT INTO virtual_card_transactions (
                card_id, account_id, transaction_type, amount, description, 
                reference_number, status, processed_by
            ) VALUES (?, ?, 'credit', ?, ?, ?, 'completed', ?)";
            
            $trans_result = $db->query($insert_transaction, [
                $card_id, $account_id, $initial_balance, 
                "Initial card balance - Card generation", $reference, $_SESSION['user_id']
            ]);
            
            if (!$trans_result) {
                throw new Exception("Failed to create initial balance transaction.");
            }
        }
        
        // Commit transaction
        $db->query("COMMIT");
        
        $success = "Virtual card generated successfully! Card ID: $card_id";
        
    } catch (Exception $e) {
        if (isset($db)) {
            $db->query("ROLLBACK");
        }
        $error = $e->getMessage();
    }
}

// Get users for dropdown
try {
    $db = getDB();
    $users_result = $db->query("SELECT id, first_name, last_name, username, account_number FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name");
    $users = [];
    while ($user = $users_result->fetch_assoc()) {
        $users[] = $user;
    }
} catch (Exception $e) {
    $users = [];
}

// Helper function to generate card number
function generateCardNumber() {
    // Generate a 16-digit card number starting with 4 (Visa-like)
    $prefix = '4000';
    $middle = '';
    for ($i = 0; $i < 8; $i++) {
        $middle .= rand(0, 9);
    }
    
    // Calculate check digit using Luhn algorithm
    $number = $prefix . $middle;
    $sum = 0;
    $alternate = false;
    
    for ($i = strlen($number) - 1; $i >= 0; $i--) {
        $digit = intval($number[$i]);
        if ($alternate) {
            $digit *= 2;
            if ($digit > 9) {
                $digit = ($digit % 10) + 1;
            }
        }
        $sum += $digit;
        $alternate = !$alternate;
    }
    
    $checkDigit = (10 - ($sum % 10)) % 10;
    return $number . $checkDigit;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="virtual-cards.php">Virtual Cards</a></li>
        <li class="breadcrumb-item active" aria-current="page">Generate Card</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row row-cards">
    <!-- Card Generation Form -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-plus-circle me-2"></i>
                    Generate New Virtual Card
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Select Account Holder <span class="text-danger">*</span></label>
                                <select name="account_id" class="form-select" required>
                                    <option value="">Choose account holder...</option>
                                    <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo (isset($_POST['account_id']) && $_POST['account_id'] == $user['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?> 
                                        (@<?php echo htmlspecialchars($user['username']); ?>) - 
                                        Acc: <?php echo htmlspecialchars($user['account_number']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Card Holder Name <span class="text-danger">*</span></label>
                                <input type="text" name="card_holder_name" class="form-control" 
                                       placeholder="Name as it appears on card" 
                                       value="<?php echo htmlspecialchars($_POST['card_holder_name'] ?? ''); ?>" required>
                                <small class="form-hint">This will be printed on the virtual card</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Daily Spending Limit <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="daily_limit" class="form-control" 
                                           step="0.01" min="1" max="50000" 
                                           value="<?php echo $_POST['daily_limit'] ?? '5000'; ?>" required>
                                </div>
                                <small class="form-hint">Maximum amount that can be spent per day</small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Monthly Spending Limit <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="monthly_limit" class="form-control" 
                                           step="0.01" min="1" max="500000" 
                                           value="<?php echo $_POST['monthly_limit'] ?? '50000'; ?>" required>
                                </div>
                                <small class="form-hint">Maximum amount that can be spent per month</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Initial Card Balance</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="initial_balance" class="form-control" 
                                           step="0.01" min="0" max="100000" 
                                           value="<?php echo $_POST['initial_balance'] ?? '0'; ?>">
                                </div>
                                <small class="form-hint">Optional: Add initial balance to the card</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-credit-card me-2"></i>
                            Generate Virtual Card
                        </button>
                        <a href="virtual-cards.php" class="btn btn-secondary ms-2">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Cards
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Guidelines -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Card Generation Guidelines
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h4 class="alert-title">Card Details</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Card number is auto-generated (16 digits)</li>
                        <li>CVV is randomly generated (3 digits)</li>
                        <li>Expiry date is set to 3 years from now</li>
                        <li>Cards are activated immediately</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h4 class="alert-title">Spending Limits</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Daily limit: $1 - $50,000</li>
                        <li>Monthly limit: $1 - $500,000</li>
                        <li>Daily limit cannot exceed monthly limit</li>
                        <li>Limits can be modified later</li>
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <h4 class="alert-title">Security Features</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Secure card number generation</li>
                        <li>Luhn algorithm validation</li>
                        <li>Encrypted storage</li>
                        <li>Transaction monitoring</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-populate card holder name when account is selected
document.querySelector('select[name="account_id"]').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        const text = selectedOption.text;
        const name = text.split(' (@')[0]; // Extract name before username
        document.querySelector('input[name="card_holder_name"]').value = name.toUpperCase();
    }
});

// Validate limits
document.querySelector('input[name="daily_limit"]').addEventListener('input', validateLimits);
document.querySelector('input[name="monthly_limit"]').addEventListener('input', validateLimits);

function validateLimits() {
    const dailyLimit = parseFloat(document.querySelector('input[name="daily_limit"]').value) || 0;
    const monthlyLimit = parseFloat(document.querySelector('input[name="monthly_limit"]').value) || 0;
    
    if (dailyLimit > monthlyLimit && monthlyLimit > 0) {
        document.querySelector('input[name="daily_limit"]').setCustomValidity('Daily limit cannot exceed monthly limit');
    } else {
        document.querySelector('input[name="daily_limit"]').setCustomValidity('');
    }
}
</script>

<?php include 'includes/admin-footer.php'; ?>
